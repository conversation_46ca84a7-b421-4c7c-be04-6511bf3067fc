<script setup lang="ts">
import type { Recordable } from '@vben/types';
import { Button } from 'ant-design-vue';
import { computed, ref } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { useVbenForm } from '#/adapter/form';
import { useBindFormSchema } from './data';
import { bindMeituanTuangou } from '#/api/manageModule';
import SelectTicket from '../selectTicket/index.vue';
const emits = defineEmits(['success']);

const formData = ref<Recordable<any>>({});
// 表单配置
const [Form, formApi] = useVbenForm({
  schema: useBindFormSchema(),
  showDefaultActions: false,
  // 大屏一行显示3个，中屏一行显示2个，小屏一行显示1个
  wrapperClass: 'grid-cols-1',
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
});
// 表单弹窗
const dealGroupId = ref('');
const scenicId = ref('');
const [Model, modelApi] = useVbenModal({
  confirmText: '确定绑定',
  async onConfirm() {
    await handleSubmit();
  },
  onOpenChange(isOpen) {
    const data = modelApi.getData<any>();
    formApi.resetForm();
    if (isOpen) {
      if (data) {
        // 创建新对象而不是直接赋值
        const newFormData = { ...data };
        // 赋值给formData.value
        newFormData.ticketId = null;
        formData.value = newFormData;
        dealGroupId.value = data.dealGroupId;
        scenicId.value = data.scenicId;
        formApi.setValues(formData.value);
      }
    }
  },
});

// 处理表单数据，返回处理后的值
const processFormValues = async () => {
  const { valid } = await formApi.validate();
  if (!valid) return null;

  const values = await formApi.getValues();
  values.ticketId = values.ticketId[0].id;
  return values;
};

// 提交表单
const handleSubmit = async () => {
  const values = await processFormValues();
  if (!values) return;
  modelApi.lock();
  try {
    await bindMeituanTuangou({ dealGroupId: dealGroupId.value, ...values });
    emits('success');
    modelApi.close();
  } catch (error) {
    modelApi.unlock();
  }
};

const selectTicketRef = ref();
const addTicketBtn = () => {
  selectTicketRef.value[0].addTicket(scenicId.value);
};
</script>

<template>
  <Model class="w-[800px]" title="绑定门票">
    <Form>
      <template #ticketId="slotProps">
        <div class="w-full">
          <Button type="primary" class="mb-2" @click="addTicketBtn"
            >添加绑定门票</Button
          >
          <SelectTicket v-bind="slotProps" ref="selectTicketRef"></SelectTicket>
        </div>
      </template>
    </Form>
  </Model>
</template>

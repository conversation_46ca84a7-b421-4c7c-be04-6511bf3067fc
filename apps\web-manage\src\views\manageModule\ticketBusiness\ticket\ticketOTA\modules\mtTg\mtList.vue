<script setup lang="ts">
import { Page, useVbenModal } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import { Button, message, Modal } from 'ant-design-vue';
import { SyncOutlined } from '@ant-design/icons-vue';
import { useColumns, useGridFormSchema } from './data';
import {
  getMeituanTuangouList,
  unbindMeituanTuangou,
} from '#/api/manageModule';
import MtSync from './mtSync.vue';
import MtBind from './mtBind.vue';
import MtVerifyLog from './mtVerifyLog.vue';
const onActionClick = ({ code, row }: { code: string; row: any }) => {
  console.log(code, row);
  if (code === 'unbind') {
    Modal.confirm({
      title: '提示',
      content: `确定要取消绑定【${row.title}】吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        await unbindMeituanTuangou({
          dealGroupId: row.dealGroupId,
        });
        onRefresh();
      },
    });
  } else if (code === 'bind') {
    bindFormApi.setData(row).open();
  } else if (code === 'verifyLog') {
    verifyLogFormApi.setData(row).open();
  }
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    fieldMappingTime: [
      ['verificationDate', ['verificationStartDate', 'verificationEndDate']],
    ],
    schema: useGridFormSchema(),
    collapsed: true,
    submitOnChange: true,
    submitOnEnter: true,
    collapsedRows: 1,
    wrapperClass:
      'grid-cols-4 md:grid-cols-2 xl:grid-cols-4 2xl:grid-cols-5 gap-2',
    showCollapseButton: true,
  },

  gridOptions: {
    columns: useColumns(onActionClick),
    // height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          const res: any = await getMeituanTuangouList({
            page: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
          return {
            items: res.list,
            total: res.total,
          };
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },
    virtualXConfig: {
      enabled: false,
    },
    virtualYConfig: {
      enabled: false,
    },
    showOverflow: false,
    toolbarConfig: {
      custom: true,
      export: false,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
  } as VxeTableGridOptions<any>,
});

const [SyncForm, syncFormApi] = useVbenModal({
  connectedComponent: MtSync,
  destroyOnClose: true,
});

const syncTicket = () => {
  syncFormApi.setData({}).open();
};
const [BindForm, bindFormApi] = useVbenModal({
  connectedComponent: MtBind,
  destroyOnClose: true,
});

const [VerifyLogForm, verifyLogFormApi] = useVbenModal({
  connectedComponent: MtVerifyLog,
  destroyOnClose: true,
});
const onRefresh = async () => {
  message.success('操作成功');
  gridApi.query();
};
</script>
<template>
  <Grid>
    <template #toolbar-actions>
      <Button type="primary" @click="syncTicket">
        <SyncOutlined />同步美团团购门票</Button
      >
    </template>
  </Grid>
  <SyncForm @success="onRefresh" />
  <BindForm @success="onRefresh" />
  <VerifyLogForm @success="onRefresh" />
</template>

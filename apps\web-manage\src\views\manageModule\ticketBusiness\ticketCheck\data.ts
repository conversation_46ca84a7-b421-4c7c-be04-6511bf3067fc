import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import { useAccessStore } from '@vben/stores';
import { getAllScenicList, getAllHandsetList, getAllGateList } from '#/api/manageModule';
import { toRefs } from 'vue';
const { accessAllEnums } = toRefs(useAccessStore());

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'name',
      label: '检票点名称',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入检票点名称',
        allowClear: true,
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'scenicId',
      label: '所属景区',
      hideLabel: true,
      componentProps: {
        // 菜单接口转options格式
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.scenicName,
            value: item.id,
          }));
        },
        // 菜单接口
        api: getAllScenicList,
        placeholder: '请选择所属景区',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '状态',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择状态',
        allowClear: true,
        options: accessAllEnums.value.status.list,
      },
    },
  ];
}

export function useColumns<T = any>(
  onActionClick: OnActionClickFn<T>,
  onStatusChange?: (newStatus: any, row: T) => PromiseLike<boolean | undefined>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'pointName',
      title: '检票点名称',
    },
    {
      field: 'freeTicketType',
      title: '免票类型',
      formatter: ({ row }: any) => {
        return row.freeTicketType === 0 ? '不免票' : '内部人员免票';
      },
    },
    {
      field: 'scenicName',
      title: '所属景区',
      formatter: ({ row }: any) => row.scenicInfo?.scenicName || '--',
    },
    {
      field: 'status',
      title: '状态',
      cellRender: {
        name: 'CellSwitch',
        attrs: {
          beforeChange: onStatusChange,
        },
      },
    },
    {
      field: 'createdBy',
      title: '创建人',
    },
    {
      field: 'createdAt',
      title: '创建时间',
    },
    {
      field: 'operation',
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'pointName',
          nameTitle: '检票点',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [{ code: 'ticket', text: '可检门票' }, 'edit', 'delete'],
      },
      fixed: 'right',
      title: '操作',
      width: 180,
    },
  ];
}

export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'pointName',
      label: '检票点名称',
      componentProps: {
        placeholder: '请输入检票点名称',
        allowClear: true,
      },
      rules: 'required',
    },
    {
      component: 'ApiSelect',
      fieldName: 'scenicId',
      label: '所属景区',
      componentProps: {
        // 菜单接口转options格式
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.scenicName,
            value: item.id,
          }));
        },
        // 菜单接口
        api: getAllScenicList,
        placeholder: '请选择所属景区',
        allowClear: true,
      },
      rules: 'selectRequired',
    },
    {
      component: 'RadioGroup',
      fieldName: 'status',
      label: '状态',
      componentProps: {
        placeholder: '请选择状态',
        allowClear: true,
        options: accessAllEnums.value.status.list,
      },
      defaultValue: 1,
    },
    {
      component: 'ApiSelect',
      fieldName: 'ticketIds',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2',
      label: '可检门票',
      rules: 'selectRequired',
      componentProps: {
        placeholder: '请选择可检门票',
        allowClear: true,
        mode: 'multiple',
      },
      // dependencies: {
      //   componentProps(values) {
      //     if (values.scenicId) {
      //       return (values.ticketIds = []);
      //     }
      //     return {};
      //   },
      //   triggerFields: ['scenicId'],
      // },
    },
    {
      component: 'ApiSelect',
      fieldName: 'machineIds',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2',
      label: '关联闸机',
      rules: 'selectRequired',
      componentProps: {
        // 菜单接口转options格式
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.deviceName,
            value: item.id,
            sn: item.deviceSn,
          }));
        },
        // 菜单接口
        api: getAllGateList,
        placeholder: '请选择关联闸机',
        allowClear: true,
        mode: 'multiple',
      },
      dependencies: {
        componentProps(values) {
          if (values.scenicId) {
            return {
              params: {
                scenicId: values.scenicId + ',0',
              },
            };
          }
          return {};
        },
        triggerFields: ['scenicId'],
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'handMachineIds',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2',
      label: '关联手持机',
      rules: 'selectRequired',
      componentProps: {
        // 菜单接口转options格式
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.deviceName,
            value: item.id,
            sn: item.deviceSn,
          }));
        },
        // 菜单接口
        api: getAllHandsetList,
        placeholder: '请选择关联手持机',
        allowClear: true,
        mode: 'multiple',
      },
      dependencies: {
        componentProps(values) {
          if (values.scenicId) {
            return {
              params: {
                scenicId: values.scenicId + ',0',
              },
            };
          }
          return {};
        },
        triggerFields: ['scenicId'],
      },
    },
    {
      component: 'RadioGroup',
      fieldName: 'freeTicketType',
      label: '免票类型',
      componentProps: {
        placeholder: '请选择免票类型',
        allowClear: true,
        options: [
          { label: '不免票', value: 0 },
          { label: '内部人员免票', value: 1 },
        ],
      },
      defaultValue: 0,
    },
  ];
}

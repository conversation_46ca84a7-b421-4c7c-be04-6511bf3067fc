import type { Recordable } from '@vben/types';
import { requestClient } from '#/api/request';

async function getMeituanConfig(params: Recordable<any>) {
  return requestClient.get('/sys/meituan/getdaozhongConfig', { params });
}
async function getMeituanAuthUrl(params: Recordable<any>) {
  return requestClient.get('/sys/meituan/getdaozhongAuthUrl', { params });
}

async function getMeituanUnauthUrl(params: Recordable<any>) {
  return requestClient.get('/sys/meituan/getDaozhongUnauthUrl', { params });
}

async function meituanAuthBind(data: Recordable<any>) {
  return requestClient.post('/sys/meituan/daozhongAuthBind', data);
}

export { getMeituanConfig, getMeituanAuthUrl, getMeituanUnauthUrl, meituanAuthBind };

<script setup lang="ts">
import { Page, useVbenModal } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import { useVerifyLogGridFormSchema, useVerifyLogColumns } from './data';
import {
  getMeituanTuangouVerifyLog,
  consumeReverseMeituanTuangou,
} from '#/api/manageModule';
import { Modal, message } from 'ant-design-vue';

const onActionClick = ({ code, row }: { code: string; row: any }) => {
  if (code === 'cancel') {
    Modal.confirm({
      title: '提示',
      content: `确定要撤销核销吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        await consumeReverseMeituanTuangou({
          verifyId: row.id,
        });
        message.success('操作成功');
        gridApi.query();
      },
    });
  }
};
// 列表数据
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    fieldMappingTime: [['verificationDate', ['startDate', 'endDate']]],
    schema: useVerifyLogGridFormSchema(),
    collapsed: true,
    submitOnChange: true,
    submitOnEnter: true,
    collapsedRows: 1,
    wrapperClass:
      'grid-cols-3 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-3 gap-2',
    showCollapseButton: true,
  },
  separator: false,
  gridOptions: {
    columns: useVerifyLogColumns(onActionClick),
    height: '600',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          const res: any = await getMeituanTuangouVerifyLog({
            page: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
          return {
            items: res.list,
            total: res.total,
          };
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },
  } as VxeTableGridOptions<any>,
});

// 核销记录弹窗
const [Model, modelApi] = useVbenModal({
  showCancelButton: false,
  confirmText: '关闭',
  async onConfirm() {
    modelApi.close();
  },
  onOpenChange(isOpen) {
    const data = modelApi.getData<any>();
    if (isOpen) {
      if (data) {
        gridApi.setGridOptions({
          proxyConfig: {
            ajax: {
              query: async ({ page }: any, formValues: any) => {
                const res: any = await getMeituanTuangouVerifyLog({
                  page: page.currentPage,
                  pageSize: page.pageSize,
                  scenicId: data.scenicId,
                  dealGroupId: data.dealGroupId,
                  ...formValues,
                });
                return {
                  items: res.list,
                  total: res.total,
                };
              },
            },
          },
        });
      }
    }
  },
});
</script>
<template>
  <Model class="w-[1200px]" title="美团团购核销记录">
    <Grid></Grid>
  </Model>
</template>

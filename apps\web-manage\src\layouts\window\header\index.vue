<script lang="ts" setup>
import { onUnmounted, ref, watch } from 'vue';

// ============================================================================
import { useUserStore } from '@vben/stores';

import { Avatar } from 'ant-design-vue';

import switchTicketTem from './switchTicket.vue';
import moreSetTem from './moreSet.vue';

const userStore = useUserStore();
const userInfo: any = ref({});
watch(
  () => userStore.userInfo,
  (newVal) => {
    userInfo.value = newVal;
  },
  { deep: true, immediate: true },
);

// 年月日时分秒 ======================================================================================================================
const currentTime: any = ref({});
const timer: any = ref(null);
const updateTime = () => {
  const weekDays = [
    '星期日',
    '星期一',
    '星期二',
    '星期三',
    '星期四',
    '星期五',
    '星期六',
  ];
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');

  // 更新页面显示
  currentTime.value = {
    ymd: `${year}年${month}月${day}日`,
    hms: `${hours}:${minutes}:${seconds}`,
    week: weekDays[now.getDay()],
  };

  // 动态计算与下一秒的时间差（精准触发）
  const delay = 1000 - now.getMilliseconds();
  setTimeout(updateTime, delay);
};
updateTime();
timer.value = setInterval(updateTime, 1000);
onUnmounted(() => {
  clearInterval(timer.value);
});
</script>

<template>
  <div class="flex items-center justify-between py-2">
    <div class="flex-center" v-if="userInfo?.tenantInfo">
      <Avatar
        :src="userInfo.tenantInfo?.tenantLogo"
        shape="square"
        :size="34"
      />
      <div class="ml-2 text-lg">{{ userInfo.tenantInfo?.tenantName }}</div>
      <div class="ml-5">
        <switchTicketTem />
      </div>
    </div>

    <div class="flex items-center gap-10 pr-3">
      <div class="flex items-center">
        <div class="text-2xl" style="font-family: 'PingFang SC'">
          {{ currentTime.hms }}
        </div>
        <div class="ml-2 border-l pl-2 text-xs">
          <div>{{ currentTime.ymd }}</div>
          <div>{{ currentTime.week }}</div>
        </div>
      </div>
      <moreSetTem>
        <div class="flex-center cursor-pointer">
          <Avatar v-if="userInfo?.avatar" :src="userInfo.avatar" :size="34" />
          <Avatar v-else :size="34">{{ userInfo?.name.charAt(0) }}</Avatar>
          <div class="ml-1 text-xs">
            <div class="text-color-info">{{ userInfo?.name }}</div>
            <div>{{ userInfo?.phone }}</div>
          </div>
        </div>
      </moreSetTem>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>

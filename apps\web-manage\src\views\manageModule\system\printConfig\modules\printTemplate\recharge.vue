<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { Checkbox, Input, Row, Col, InputNumber, Button } from 'ant-design-vue';

const props = defineProps({
  value: {
    type: Object,
    default: () => ({}),
  },
});
const emit = defineEmits(['update:value']);
// 配置项状态
let config = reactive({
  // 页面设置
  page: {
    width: 58, // 小票纸宽度(mm) - 常见58mm热敏纸
    marginTop: 2,
    marginBottom: 2,
    marginLeft: 2,
    marginRight: 2,
  },
  // 抬头设置
  header: {
    areaName: true,
    ticketType: true,
  },
  // 顶部设置
  top: {
    orderNumber: true,
    saleWindow: true,
    saleTime: true,
    operator: true,
  },
  // 中部设置
  middle: {
    memberName: true,
    memberCard: true,
    phone: true,
  },
  // 底部设置
  bottom: {
    receivedAmount: true,
    actualAmount: true,
    paymentMethod: true,
    change: true,
  },
  // 尾注设置
  footer: {
    note1: true,
    note1Text: '',
    note2: true,
    note2Text: '',
  },
});

// 计算预览区样式 - 按实际小票比例
const previewStyle = computed(() => ({
  width: `${config.page.width}mm`, // 58mm = 232px
  padding: `${config.page.marginTop}mm ${config.page.marginRight}mm ${config.page.marginBottom}mm ${config.page.marginLeft}mm`,
  fontFamily: 'monospace', // 使用等宽字体模拟小票打印效果
}));

watch(
  () => config,
  (newConfig) => {
    emit('update:value', {
      margins: [
        newConfig.page.marginLeft,
        newConfig.page.marginTop,
        newConfig.page.marginRight,
        newConfig.page.marginBottom,
      ],
      width: newConfig.page.width,
      templateConfig: newConfig,
    });
  },
  { deep: true, immediate: true },
);
watch(
  () => props.value,
  (newValue) => {
    if (newValue && newValue.templateConfig) {
      Object.assign(config, newValue.templateConfig);
    }
  },
  {
    immediate: true,
    deep: true,
  },
);
import { print } from '../print';
const printTest = () => {
  print(config.page.width);
};
</script>

<template>
  <div class="flex gap-6">
    <!-- 左侧预览区 -->
    <div class="w-1/3">
      <h3 class="mb-4 text-lg font-semibold">模板设计</h3>
      <div
        class="flex min-h-[600px] flex-col items-center justify-center border border-gray-300 bg-gray-50 p-4"
      >
        <div id="printContent">
          <!-- 小票预览 -->
          <div class="text-13 bg-white shadow-lg" :style="previewStyle">
            <!-- 抬头 -->
            <div
              v-if="config.header.areaName || config.header.ticketType"
              class="mb-1 text-center"
            >
              <div
                v-if="config.header.areaName"
                class="text-14 font-bold leading-7"
              >
                演示景区
              </div>
              <div v-if="config.header.ticketType" class="text-12 leading-7">
                充值小票【开卡充值】
              </div>
            </div>

            <!-- 分割线 -->
            <div
              v-if="config.header.areaName || config.header.ticketType"
              class="divider dashed"
            ></div>

            <!-- 顶部信息 -->
            <div class="leading-normal">
              <div v-if="config.top.orderNumber" class="mb-1.5 flex">
                <span class="lastJustify w56 block"
                  >订<span></span>单<span></span>号</span
                >
                <span>：</span>
                <span class="flex-1">12345678990</span>
              </div>
              <div v-if="config.top.saleWindow" class="mb-1.5 flex">
                <span class="lastJustify w56"
                  >售<span></span>票<span></span>窗<span></span>口</span
                >
                <span>：</span>

                <span class="flex-1">大门1号窗口</span>
              </div>
              <div v-if="config.top.saleTime" class="mb-1.5 flex">
                <span class="lastJustify w56"
                  >售<span></span>票<span></span>时<span></span>间</span
                >
                <span>：</span>
                <span class="flex-1">2025-07-23 11:38:38</span>
              </div>
              <div v-if="config.top.operator" class="mb-1.5 flex">
                <span class="lastJustify w56"
                  >操<span></span>作<span></span>员</span
                >
                <span>：</span>
                <span class="flex-1">售票员A</span>
              </div>
            </div>

            <!-- 分割线 -->
            <div
              v-if="
                config.top.orderNumber ||
                config.top.saleWindow ||
                config.top.saleTime ||
                config.top.operator
              "
              class="divider dashed"
            ></div>

            <!-- 中部信息 -->
            <div class="leading-normal">
              <div v-if="config.middle.memberName" class="mb-1.5 flex">
                <span class="lastJustify w56">姓<span></span>名</span>
                <span>：</span>
                <span class="flex-1">XXX</span>
              </div>
              <div v-if="config.middle.memberCard" class="mb-1.5 flex">
                <span class="lastJustify w56">卡<span></span>号</span>
                <span>：</span>
                <span class="flex-1">12345674890</span>
              </div>
              <div v-if="config.middle.phone" class="mb-1.5 flex">
                <span class="lastJustify w56"
                  >手<span></span>机<span></span>号</span
                >
                <span>：</span>
                <span class="flex-1">138****8888</span>
              </div>
              <div class="mb-1.5 flex">
                <span class="lastJustify w56"
                  >充<span></span>值<span></span>金<span></span>额</span
                >
                <span>：</span>
                <span class="flex-1">1200.00</span>
              </div>
              <div class="mb-1.5 flex">
                <span class="lastJustify w56"
                  >赠<span></span>送<span></span>金<span></span>额</span
                >
                <span>：</span>
                <span class="flex-1">0.00</span>
              </div>
            </div>

            <!-- 分割线 -->
            <div class="divider dashed"></div>

            <!-- 底部信息 -->
            <div class="leading-normal">
              <div v-if="config.bottom.receivedAmount" class="mb-1.5 flex">
                <span class="lastJustify w56"
                  >应<span></span>收<span></span>金<span></span>额</span
                >
                <span>：</span>
                <span class="flex-1">1200.00</span>
              </div>
              <div v-if="config.bottom.actualAmount" class="mb-1.5 flex">
                <span class="lastJustify w56"
                  >实<span></span>收<span></span>金<span></span>额</span
                >
                <span>：</span>
                <span class="flex-1">1200.00</span>
              </div>
              <div v-if="config.bottom.paymentMethod" class="mb-1.5 flex">
                <span class="lastJustify w56"
                  >结<span></span>算<span></span>方<span></span>式</span
                >
                <span>：</span>
                <span class="flex-1">扫码</span>
              </div>
              <div v-if="config.bottom.change" class="mb-1.5 flex">
                <span class="lastJustify w56">找<span></span>零</span
                ><span>：</span>
                <span class="flex-1">0.00</span>
              </div>
            </div>

            <!-- 分割线 -->
            <div
              v-if="config.footer.note1 || config.footer.note2"
              class="divider dashed"
            ></div>

            <!-- 尾注 -->
            <div
              v-if="config.footer.note1 || config.footer.note2"
              class="text-center leading-tight"
            >
              <div v-if="config.footer.note1" class="mb-1">
                {{ config.footer.note1Text || 'XXXXXX景区' }}
              </div>
              <div v-if="config.footer.note2">
                {{ config.footer.note2Text || '欢迎下次光临' }}
              </div>
            </div>
          </div>
        </div>
        <div class="text-center text-xs text-gray-500">*以实际打印效果为准</div>
      </div>
    </div>

    <!-- 右侧配置区 -->
    <div class="w-2/3">
      <div class="space-y-6">
        <!-- 页面设置 -->
        <div>
          <h4 class="mb-3 font-semibold">页面设置</h4>

          <Row :gutter="16">
            <Col :span="24">
              <div class="mb-2 flex items-center gap-2">
                <span class="w-16 text-[14px]">纸张宽度:</span>
                <InputNumber
                  v-model:value="config.page.width"
                  :min="40"
                  :max="80"
                  size="small"
                />
                <span class="text-xs text-gray-500">mm (常用: 58mm/80mm)</span>
              </div>
            </Col>
          </Row>
          <Row :gutter="8">
            <Col :span="12">
              <div class="flex items-center gap-2">
                <span class="w-12 text-[14px]">上边距:</span>
                <InputNumber
                  v-model:value="config.page.marginTop"
                  :min="0"
                  :max="10"
                  size="small"
                />
                <span class="text-xs text-gray-500">mm</span>
              </div>
            </Col>
            <Col :span="12">
              <div class="flex items-center gap-2">
                <span class="w-12 text-[14px]">下边距:</span>
                <InputNumber
                  v-model:value="config.page.marginBottom"
                  :min="0"
                  :max="10"
                  size="small"
                />
                <span class="text-xs text-gray-500">mm</span>
              </div>
            </Col>
          </Row>
          <Row :gutter="8" class="mt-2">
            <Col :span="12">
              <div class="flex items-center gap-2">
                <span class="w-12 text-[14px]">左边距:</span>
                <InputNumber
                  v-model:value="config.page.marginLeft"
                  :min="0"
                  :max="10"
                  size="small"
                />
                <span class="text-xs text-gray-500">mm</span>
              </div>
            </Col>
            <Col :span="12">
              <div class="flex items-center gap-2">
                <span class="w-12 text-[14px]">右边距:</span>
                <InputNumber
                  v-model:value="config.page.marginRight"
                  :min="0"
                  :max="10"
                  size="small"
                />
                <span class="text-xs text-gray-500">mm</span>
              </div>
            </Col>
          </Row>
        </div>

        <!-- 抬头设置 -->
        <div>
          <h4 class="mb-3 font-semibold">抬头设置</h4>
          <Row :gutter="16">
            <Col :span="12">
              <Checkbox v-model:checked="config.header.areaName"
                >景区名称</Checkbox
              >
            </Col>
            <Col :span="12">
              <Checkbox v-model:checked="config.header.ticketType"
                >小票类型</Checkbox
              >
            </Col>
          </Row>
        </div>

        <!-- 顶部设置 -->
        <div>
          <h4 class="mb-3 font-semibold">顶部设置</h4>
          <Row :gutter="16">
            <Col :span="12">
              <Checkbox v-model:checked="config.top.orderNumber"
                >订单号</Checkbox
              >
            </Col>
            <Col :span="12">
              <Checkbox v-model:checked="config.top.saleWindow"
                >售票窗口</Checkbox
              >
            </Col>
            <Col :span="12">
              <Checkbox v-model:checked="config.top.saleTime"
                >售票时间</Checkbox
              >
            </Col>
            <Col :span="12">
              <Checkbox v-model:checked="config.top.operator">操作员</Checkbox>
            </Col>
          </Row>
        </div>

        <!-- 中部设置 -->
        <div>
          <h4 class="mb-3 font-semibold">中部设置</h4>
          <Row :gutter="16">
            <Col :span="12">
              <Checkbox v-model:checked="config.middle.memberName"
                >姓名</Checkbox
              >
            </Col>
            <Col :span="12">
              <Checkbox v-model:checked="config.middle.memberCard"
                >卡号</Checkbox
              >
            </Col>
            <Col :span="12">
              <Checkbox v-model:checked="config.middle.phone">手机号</Checkbox>
            </Col>
          </Row>
        </div>

        <!-- 底部设置 -->
        <div>
          <h4 class="mb-3 font-semibold">底部设置</h4>
          <Row :gutter="16">
            <Col :span="12">
              <Checkbox v-model:checked="config.bottom.receivedAmount"
                >应收金额</Checkbox
              >
            </Col>
            <Col :span="12">
              <Checkbox v-model:checked="config.bottom.actualAmount"
                >实收金额</Checkbox
              >
            </Col>
            <Col :span="12">
              <Checkbox v-model:checked="config.bottom.paymentMethod"
                >结算方式</Checkbox
              >
            </Col>
            <Col :span="12">
              <Checkbox v-model:checked="config.bottom.change">找零</Checkbox>
            </Col>
          </Row>
        </div>

        <!-- 尾注设置 -->
        <div>
          <h4 class="mb-3 font-semibold">尾注设置</h4>
          <div class="space-y-3">
            <div class="flex items-center gap-2">
              <Checkbox v-model:checked="config.footer.note1" class="w-[80px]"
                >尾注1</Checkbox
              >
              <Input
                v-model:value="config.footer.note1Text"
                placeholder="请输入尾注1内容"
                size="small"
              />
            </div>
            <div class="flex items-center gap-2">
              <Checkbox v-model:checked="config.footer.note2" class="w-[80px]"
                >尾注2</Checkbox
              >
              <Input
                v-model:value="config.footer.note2Text"
                placeholder="请输入尾注2内容"
                size="small"
              />
            </div>
          </div>
        </div>
        <Button type="primary" @click="printTest">打印测试</Button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.text-12 {
  font-size: 12px;
}
.text-13 {
  font-size: 13px;
}
.text-14 {
  font-size: 14px;
}
.leading-7 {
  line-height: 1.75;
}
.w56 {
  width: 56px;
}
.lastJustify {
  text-align: justify;
  text-align-last: justify;
}
/* 添加分割线样式 */
.divider {
  height: 1px;
  border-bottom: 1px dashed rgba(0, 0, 0, 1);
  margin: 4px 0;
  position: relative;
}
</style>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { meituanAuthBind } from '#/api/manageModule';
import { message, Spin, Result, Button } from 'ant-design-vue';
import { useRoute } from 'vue-router';
const route = useRoute();

const loading = ref(false);
const result = ref<any>({
  code: 200,
});
onMounted(async () => {
  loading.value = true;
  try {
    await meituanAuthBind({
      code: route.query.code ||'74a9a50bad4a85d94bac6b03eae54b0a',
      businessId: route.query.businessId || 58,
      state: route.query.state||'j8yZkS3BWx',
    });
  } catch (error: any) {
    result.value = error;
  }
  loading.value = false;
});

const close = () => {
  if (window.opener && window.opener.parentMethod) {
    window.opener.parentMethod();
    window.close();
  } else {
    message.error('无法访问父窗口或 parentMethod 方法');
  }
};
</script>
<template>
  <div class="flex h-full w-full items-center justify-center">
    <Spin size="large" :spinning="loading" tip="授权绑定中...">
      <Result
        :status="result.code == 200 ? 'success' : 'error'"
        :title="result.code == 200 ? '授权成功' : result.message"
      >
        <template #extra>
          <Button type="primary" @click="close()">关闭</Button>
        </template>
      </Result>
    </Spin>
  </div>
</template>

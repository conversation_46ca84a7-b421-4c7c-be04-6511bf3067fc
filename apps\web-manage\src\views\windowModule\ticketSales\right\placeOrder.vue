<script lang="ts" setup>
import eventBus from '#/utils/eventBus';
import { ref, computed, h } from 'vue';
import { useVbenModal, alert } from '@vben/common-ui';

import {
  Form,
  FormItem,
  Input,
  Radio,
  RadioGroup,
  RadioButton,
  message,
  Result,
} from 'ant-design-vue';
import { CheckOutlined } from '@ant-design/icons-vue';

import { useAccessStore } from '@vben/stores';
const { accessAllEnums } = useAccessStore();

let props = defineProps(['searchInfo']);

import {
  ticketCreateOrder,
  ticketOrderQuery,
} from '#/api/windowModule/ticketSales';
import payIcon1 from '#/assets/payIcon/1.svg';
import payIcon2 from '#/assets/payIcon/2.svg';
import payIcon4 from '#/assets/payIcon/4.svg';
import payIcon5 from '#/assets/payIcon/5.svg';
let payIconMap: any = ref({
  1: payIcon1,
  2: payIcon2,
  3: payIcon2,
  4: payIcon4,
  5: payIcon5,
  99: payIcon2,
});

// ========================================================================================
let openVal: any = ref([]);
const open = async (e: any) => {
  openVal.value = e;

  let arr = [];
  for (let i = 0; i < openVal.value.length; i++) {
    let item = openVal.value[i];
    let params: any = {
      ticketId: item.id,
      ticketNum: item.ticketNum,
      ticketUnitPrice: item.sellingPrice,
      touristList: item.touristList,
    };
    if (item.periodId) params.periodId = item.periodId;
    arr.push(params);
  }
  formState.value.ticketList = arr;

  modalApi.open();
};
defineExpose({ open });

// ========================================================================================
import math from '#/utils/math';
const totalAmount = computed(() => {
  return openVal.value.reduce((acc: any, cur: any) => {
    return math.add(acc, math.multiply(cur.sellingPrice, cur.ticketNum));
  }, 0);
});

// ========================================================================================
import oneCardTem from './oneCard.vue';
let oneCardTemRef: any = ref(null);
let oneCardParams: any = ref({});
const oneCarCancel = () => {
  if (!oneCardParams.value.id) {
    oneCardParams.value = {};
    delete formState.value.payMethod;
  }
};
const oneCarConfirm = (e: any) => {
  let isTrue = +e.balance >= totalAmount.value;
  if (!isTrue) {
    message.error('余额不足');
    oneCarCancel();
    return;
  }
  oneCardParams.value = e;
};

// =====================================================================================================================
import scanCodeTem from './scanCode.vue';
let scanCodeTemRef: any = ref(null);
let scanCodeParams: any = ref('');
const scanCodeCancel = (e: any) => {
  if (!e) {
    scanCodeParams.value = '';
    delete formState.value.payMethod;
    return;
  }
  scanCodeParams.value = e;
  sublimeBtn();
};

// =====================================================================================================================
const payMethodChange = (e: any) => {
  if (e.value === 1) {
    oneCardTemRef.value.open(formState.value);
    return;
  }
  oneCardParams.value = {};
  delete formState.value.payMethod;

  if (e.value === 3) {
    scanCodeParams.value = '';
    scanCodeTemRef.value.open(formState.value);
  }
};

// =========================================================================================
const formRef: any = ref(null);
const rules: any = {
  payMethod: [{ required: true, message: '请选择支付方式' }],
};
const formState: any = ref({});
let isPrint = ref<Number>(1);

const [Modal, modalApi] = useVbenModal({
  destroyOnClose: true,
  fullscreenButton: false,
  closable: false,
  confirmText: '下单',
  header: false,
  closeOnClickModal: false,
  onCancel() {
    modalApi.close();
    formState.value = {};
    isPrint.value = 1;
    oneCardParams.value = {};
    scanCodeParams.value = '';
  },
  onConfirm() {
    sublimeBtn();
  },
});

const sublimeBtn = () => {
  formRef.value.validate().then(async () => {
    modalApi.lock();

    let params: any = { ...formState.value };
    params.playDate = props.searchInfo.playDate;

    if (params.payMethod === 1) {
      params.prepaidCardId = oneCardParams.value.id;
    }

    if (params.payMethod === 3) {
      params.payCode = scanCodeParams.value;
    }

    await ticketCreateOrder(params)
      .then(async (resData: any) => {
        if (resData.status === 'PAYING') {
          useIntervalFn(resData);
        } else if (resData.status === 'SUCCESS') {
          modalApi.close();
          formState.value = {};
          eventBus.emit('ticketSalesClearAllData');
          message.success('下单支付成功');
          if (isPrint.value) {
            let res = await ticketOrderQuery({ orderId: resData.orderId });
            drawBill(res);
          }
        } else {
          modalApi.unlock();
          message.error('下单支付失败');
        }
      })
      .catch(() => {
        modalApi.unlock();
        if (formState.value.payMethod === 3) {
          delete formState.value.payMethod;
        }
      });
  });
};

// 轮询 =========================================================================================
let countTime = ref(0);
const useIntervalFn = async (e: any) => {
  let resData = await ticketOrderQuery({ orderId: e.orderId });
  if (resData.payStatus === 1) {
    if (countTime.value >= 60) {
      //支付超时处理/未获取到支付结果处理
      alert({
        buttonAlign: 'center',
        content: h(Result, {
          status: 'error',
          title: '未查询到订单支付结果!',
        }),
      });
      countTime.value = 0;
      modalApi.unlock();
      return;
    }
    //支付中继续轮询
    setTimeout(() => {
      countTime.value++;
      useIntervalFn(e);
    }, 1000);
  } else if (resData.payStatus === 2) {
    //支付成功
    countTime.value = 0;
    modalApi.close();
    formState.value = {};
    message.success('支付成功');
    eventBus.emit('ticketSalesClearAllData');
    drawBill(resData);
  } else {
    //支付失败
    alert({
      buttonAlign: 'center',
      content: h(Result, {
        status: 'warning',
        title: '支付失败',
      }),
    });
    countTime.value = 0;
    modalApi.unlock();
  }
};

// 出票 ======================================================================================================================
const drawBill = (e: any) => {};
</script>

<template>
  <Modal title="结算" class="w-[600px]">
    <div class="mb-5 flex items-center justify-between pl-2 pr-3">
      <div class="text-lg font-bold">结算</div>
      <div>
        <span>共 </span>
        <span class="text-color-error">{{ openVal.length }}</span>
        <span> 张，合计</span>
        <span class="text-color-error">￥</span>
        <span class="text-color-error text-xl font-bold">{{
          totalAmount
        }}</span>
        <span> 元</span>
      </div>
    </div>

    <Form :model="formState" :rules="rules" ref="formRef">
      <div class="bg-color-quickBox mb-5 overflow-hidden rounded-lg p-4">
        <div class="grid grid-cols-2 gap-x-4">
          <FormItem label="联系人" class="w-full">
            <Input v-model:value="formState.userName" placeholder="请输入" />
          </FormItem>

          <FormItem label="手机号码" class="w-full">
            <Input v-model:value="formState.userPhone" placeholder="请输入" />
          </FormItem>

          <FormItem label="证件号" class="w-full">
            <Input v-model:value="formState.userIdcard" placeholder="请输入" />
          </FormItem>

          <FormItem label="备注">
            <Input v-model:value="formState.remark" placeholder="请输入" />
          </FormItem>
        </div>

        <FormItem label="直接出票" class="m-0">
          <RadioGroup v-model:value="isPrint" button-style="solid">
            <RadioButton :value="1">是</RadioButton>
            <RadioButton :value="0">否</RadioButton>
          </RadioGroup>
        </FormItem>
      </div>

      <FormItem label="支付方式" name="payMethod">
        <RadioGroup v-model:value="formState.payMethod" class="payBox">
          <Radio
            v-for="item in accessAllEnums.orderPayMethod.list"
            :value="item.value"
          >
            <div
              class="relative flex w-full items-center overflow-hidden rounded-lg border p-2"
              :class="
                formState.payMethod === item.value ? 'border-color-primary' : ''
              "
              @click="payMethodChange(item)"
            >
              <img
                :src="payIconMap[item.value]"
                alt="item.label"
                class="mr-1.5 h-[20px] w-[20px]"
              />
              <div>{{ item.label }}</div>

              <div v-if="formState.payMethod === item.value">
                <div
                  class="bg-color-primary flex-center absolute bottom-0 right-0 translate-x-4 translate-y-4 rotate-45 p-2"
                >
                  <CheckOutlined class="opacity-0" />
                </div>
                <CheckOutlined
                  class="text-color-white absolute bottom-0 right-0 scale-75 text-[12px]"
                />
              </div>
            </div>
          </Radio>
        </RadioGroup>
        <div
          v-if="formState.payMethod === 1 && oneCardParams.id"
          class="bg-color-quickBox mt-2 flex items-center rounded-lg px-4 py-2 text-xs"
        >
          <div class="font-bold">一卡通信息：</div>
          <div>卡号：{{ oneCardParams.cardNo }}</div>
          <div class="ml-4">
            余额：￥<span class="text-color-error text-base font-bold">
              {{ oneCardParams.balance }}
            </span>
            元
          </div>
        </div>
      </FormItem>
    </Form>
  </Modal>

  <oneCardTem
    ref="oneCardTemRef"
    @cancel="oneCarCancel"
    @confirm="oneCarConfirm"
  />
  <scanCodeTem
    ref="scanCodeTemRef"
    :totalAmount="totalAmount"
    @cancel="scanCodeCancel"
  />
</template>

<style lang="scss" scoped>
:deep(.ant-radio-group.payBox) {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  label {
    margin: 0;
    .ant-radio {
      display: none !important;
    }
    span.ant-radio + * {
      width: 100%;
      padding: 0;
    }
  }
}
</style>

// src/utils/eventBus.ts
import type { Emitter } from 'mitt';

// ===========================================================================================================
import { onBeforeUnmount } from 'vue';

import mitt from 'mitt';

// 定义事件类型（可选，方便 TS 类型提示）
type Events = {
  notify: string;
  // 示例事件，按需添加
  'user:login': { username: string };
  'user:logout': void;
  // 也可使用通配符： [event: string]: any
};

// 创建 mitt 实例
const emitter: Emitter<any> = mitt<any>();

export default emitter;
export const useEventBus: any = (
  event: any,
  handler: Parameters<typeof emitter.on>[1],
) => {
  emitter.on(event, handler);

  onBeforeUnmount(() => {
    emitter.off(event, handler);
  });
};



// 是否为空对象
export const isEmptyObject = (obj: object) => {
  return Object.keys(obj).length === 0;
}

// 判断是否有小数
export const isDecimal = (num: any) => {
  return parseInt(num) !== num;
}

// 判断文件类型
const imageExtensions = ["jpg", "jpeg", "png", "gif", "svg", "bmp", "webp", "base64",];
const videoExtensions = ["mp4", "m2v", "mkv", "wmv", "avi", "flv", "mov", "m4v",];
const radioExtensions = ["mp3", "wav", "wmv", 'aac', 'm4a'];
const excelExtensions = ["xls", "xlsx"];
const wordExtensions = ["doc", "docx"];
const pdfExtensions = ["pdf"];
const pptExtensions = ["ppt", "pptx"];

export const getMediaType = (url: any) => {
  const extension = url.split(".").pop().toLowerCase();
  if (imageExtensions.includes(extension)) {
    return "image"
  } else if (videoExtensions.includes(extension)) {
    return "video"
  } else if (radioExtensions.includes(extension)) {
    return "radio"
  } else if (excelExtensions.includes(extension)) {
    return "excel"
  } else if (wordExtensions.includes(extension)) {
    return "word"
  } else if (pdfExtensions.includes(extension)) {
    return "pdf"
  } else if (pptExtensions.includes(extension)) {
    return "ppt"
  } else {
    return 'image'
  }
};

export const getMediaUpType = (extension: any) => {
  if (extension === "image") {
    return { fileName: '图片', fileAccept: 'image/*' };
  } else if (extension === "video") {
    return { fileName: '视频', fileAccept: 'video/*' };
  } else if (extension === "radio") {
    return { fileName: '音频', fileAccept: 'audio/*' };
  } else if (extension === "excel") {
    return { fileName: 'excel表格', fileAccept: 'application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' };
  } else if (extension === "word") {
    return { fileName: 'word文档', fileAccept: 'application/msword, application/vnd.openxmlformats-officedocument.wordprocessingml.document' };
  } else if (extension === "pdf") {
    return { fileName: 'pdf文件', fileAccept: 'application/pdf' };
  } else if (extension === "ppt") {
    return { fileName: 'ppt文件', fileAccept: 'application/vnd.ms-powerpoint, application/vnd.openxmlformats-officedocument.presentationml.presentation' };
  } else {
    return { fileName: '图片', fileAccept: '.' };
  }
};

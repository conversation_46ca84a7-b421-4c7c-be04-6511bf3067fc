<script lang="ts">
const defaultConfig = {
  ladder: [
    {
      start: undefined,
      end: undefined,
      rent: undefined,
    },
  ],
  everyRent: undefined,
  topCap: undefined,
};
</script>
<script setup lang="ts">
import { ref, reactive, watch, nextTick, computed } from 'vue';
import { Button, InputNumber } from 'ant-design-vue';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue';

// 时间段租金配置项接口
interface TimeRangeConfig {
  start: number | undefined;
  end: number | undefined;
  rent: number | undefined;
}

// 超出配置接口
interface OverageConfig {
  everyRent: number | undefined;
  topCap: number | undefined;
}

// 计费配置接口
interface BillingConfig {
  ladder: TimeRangeConfig[];
  everyRent: number | undefined;
  topCap: number | undefined;
}

const props = defineProps({
  value: {
    type: Object,
    default: () => defaultConfig,
  },
  mode: {
    type: String,
    default: 'minute',
  },
});

const emit = defineEmits(['update:value']);

// 响应式数据
const ladder = ref<TimeRangeConfig[]>([]);
const overageConfig = reactive<OverageConfig>({ everyRent: 0, topCap: 0 });

// 计算属性：时间单位显示
const timeUnit = computed(() => {
  return props.mode === 'minute' ? '分钟' : '小时';
});

// 计算属性：最大时间值
const maxTime = computed(() => {
  return props.mode === 'minute' ? 1440 : 24; // 分钟最大1440(24小时)，小时最大24
});

// 计算属性：时间精度
const timePrecision = computed(() => {
  return props.mode === 'minute' ? 0 : 1; // 都是整数
});

// 防止循环更新的标志
let isUpdating = false;

// 初始化数据
const initializeData = (value: any) => {
  // 避免循环更新
  if (isUpdating) {
    isUpdating = false;
    return;
  }

  if (value) {
    ladder.value = value.ladder ? JSON.parse(JSON.stringify(value.ladder)) : [];
    overageConfig.everyRent = value.everyRent || 0;
    overageConfig.topCap = value.topCap || 0;
  } else {
    ladder.value = [...defaultConfig.ladder];
    overageConfig.everyRent = defaultConfig.everyRent;
    overageConfig.topCap = defaultConfig.topCap;
  }
};

watch(
  () => props.mode,
  (newValue) => {
    console.log(newValue, 'newValue');
  },
  { deep: true, immediate: true },
);

// 监听外部传入的值变化
watch(
  () => props.value,
  (newValue) => {
    initializeData(newValue);
  },
  { immediate: true, deep: true },
);

// 发出更新事件
const emitUpdate = () => {
  nextTick(() => {
    isUpdating = true;
    emit('update:value', {
      ladder: ladder.value,
      everyRent: overageConfig.everyRent,
      topCap: overageConfig.topCap,
    });
  });
};

// 监听本地数据变化并触发更新事件，但避免深度监听
watch(ladder, emitUpdate, { deep: true });
watch(() => overageConfig.everyRent, emitUpdate);
watch(() => overageConfig.topCap, emitUpdate);

// 添加时间段配置
const addTimeRangeConfig = () => {
  ladder.value.push({
    start: undefined,
    end: undefined, // 分钟默认60，小时默认2
    rent: undefined,
  });
};

// 删除时间段配置
const removeTimeRangeConfig = (index: number) => {
  ladder.value.splice(index, 1);
};

// 获取配置数据（向后兼容）
const getBillingConfig = () => {
  return {
    ladder: ladder.value,
    everyRent: overageConfig.everyRent,
    topCap: overageConfig.topCap,
  };
};

// 设置配置数据（向后兼容）
const setBillingConfig = (config: BillingConfig) => {
  if (isUpdating) {
    isUpdating = false;
    return;
  }

  if (config.ladder) {
    ladder.value = [...config.ladder];
  }
  if (config.everyRent !== undefined) {
    overageConfig.everyRent = config.everyRent;
  }
  if (config.topCap !== undefined) {
    overageConfig.topCap = config.topCap;
  }
};

// 暴露方法给父组件（向后兼容）
defineExpose({
  getBillingConfig,
  setBillingConfig,
});
</script>

<template>
  <div class="billing-config">
    <!-- 时间段租金配置 -->
    <div class="config-section">
      <div
        v-for="(config, index) in ladder"
        :key="index"
        class="time-range-item"
      >
        <div class="time-range-row">
          <!-- <span class="label">时间段租金</span> -->
          <InputNumber
            v-model:value="config.start"
            :min="0"
            :max="maxTime"
            :precision="timePrecision"
            size="small"
            class="hour-input"
          />

          <span class="separator">至</span>

          <InputNumber
            v-model:value="config.end"
            :min="config.start"
            :max="maxTime"
            :precision="timePrecision"
            size="small"
            class="hour-input"
          />

          <span class="unit">{{ timeUnit }}，租金</span>

          <InputNumber
            v-model:value="config.rent"
            :min="0"
            :precision="2"
            size="small"
            class="rate-input"
          />

          <div class="action-buttons">
            <Button
              v-if="index === ladder.length - 1"
              type="primary"
              shape="circle"
              size="small"
              @click="addTimeRangeConfig"
            >
              <PlusOutlined />
            </Button>

            <Button
              v-if="ladder.length > 1"
              danger
              shape="circle"
              size="small"
              @click="removeTimeRangeConfig(index)"
            >
              <DeleteOutlined />
            </Button>
          </div>
        </div>
      </div>
    </div>

    <!-- 超出配置 -->
    <div class="config-section">
      <div class="overage-row">
        <span class="label">超出后，每{{ timeUnit }}租金</span>

        <InputNumber
          v-model:value="overageConfig.everyRent"
          :min="0"
          :precision="2"
          size="small"
          class="rate-input"
        />

        <span class="separator">，封顶</span>

        <InputNumber
          v-model:value="overageConfig.topCap"
          :min="0"
          :precision="2"
          size="small"
          class="rate-input"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.billing-config {
  padding: 16px;
  background: #fff;
  border-radius: 4px;
}

.config-section {
  margin-bottom: 20px;
}

.time-range-item {
  margin-bottom: 12px;
}

.time-range-row,
.overage-row {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.label {
  font-size: 14px;
  color: #666;
  white-space: nowrap;
}

.separator,
.unit {
  font-size: 14px;
  color: #666;
  white-space: nowrap;
}

.hour-input {
  width: 80px;
}

.rate-input {
  width: 100px;
}

.action-buttons {
  display: flex;
  gap: 4px;
  margin-left: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .time-range-row,
  .overage-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .action-buttons {
    margin-left: 0;
    align-self: flex-end;
  }
}
</style>

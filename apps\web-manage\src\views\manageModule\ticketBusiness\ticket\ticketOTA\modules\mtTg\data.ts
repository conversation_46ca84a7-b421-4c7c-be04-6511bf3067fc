import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import { useAccessStore } from '@vben/stores';
import { getAllScenicList, getTicketAllList } from '#/api/manageModule';
import { toRefs } from 'vue';
const { accessAllEnums } = toRefs(useAccessStore());

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'ApiSelect',
      fieldName: 'scenicId',
      label: '所属景区',
      hideLabel: true,
      componentProps: {
        // 菜单接口转options格式
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.scenicName,
            value: item.id,
          }));
        },
        // 菜单接口
        api: getAllScenicList,
        placeholder: '请选择所属景区',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'name',
      label: '门票名称',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入门票名称',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'dealGroupId',
      label: '团购ID',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入团购ID',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'saleStatus',
      label: '售卖状态',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择售卖状态',
        allowClear: true,
        options: [
          { label: '未开始售卖', value: 1 },
          { label: '售卖中', value: 2 },
          { label: '售卖结束', value: 3 },
        ],
      },
    },
  ];
}

export function useColumns<T = any>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  return [
    {
      title: '团购ID',
      field: 'dealGroupId',
      width: 120,
      align: 'center',
      fixed: 'left',
    },
    {
      title: '团购名称',
      field: 'title',
      minWidth: 260,
      align: 'left',
    },
    {
      title: '所属景区',
      field: 'scenicInfo',
      width: 120,
      align: 'center',
      formatter: ({ row }: any) => {
        return row.scenicInfo?.scenicName;
      },
    },
    {
      title: '门票信息',
      field: 'ticketInfo',
      minWidth: 200,
      align: 'center',
      formatter: ({ row }: any) => {
        return row.ticketInfo?.ticketName;
      },
    },
    {
      title: '团购价格',
      field: 'price',
      width: 120,
      align: 'center',
    },
    {
      title: '售卖时间',
      field: 'beginDate',
      width: 200,
      align: 'center',
      formatter: ({ row }: any) => {
        return row.beginDate + ' \n 至 ' + row.endDate;
      },
    },
    {
      title: '服务时间',
      field: 'receiptBeginDate',
      width: 200,
      align: 'center',
      formatter: ({ row }: any) => {
        return row.receiptBeginDate + ' \n 至 ' + row.receiptEndDate;
      },
    },
    {
      title: '售卖状态',
      field: 'saleStatus',
      width: 120,
      align: 'center',
      cellRender: {
        name: 'CellTag',
        options: [
          { value: 1, label: '未开始售卖', color: 'default' },
          { value: 2, label: '售卖中', color: 'success' },
          { value: 3, label: '售卖结束', color: 'default' },
        ],
      },
    },
    {
      title: '团购状态',
      field: 'dealGroupStatus',
      width: 120,
      align: 'center',
      formatter: ({ row }: any) => {
        return row.dealGroupStatus === 1 ? '在售团单' : '隐藏单';
      },
    },
    {
      title: '操作',
      field: 'operation',
      cellRender: {
        attrs: {
          nameField: 'title',
          nameTitle: '团购名称',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            text: '绑定',
            code: 'bind',
            show: (row: any) => {
              return row.ticketId == 0;
            },
          },
          {
            text: '解绑',
            code: 'unbind',
            danger: true,
            show: (row: any) => {
              return row.ticketId != 0;
            },
          },
          { text: '核销记录', code: 'verifyLog' },
        ],
      },
      width: 180,
      align: 'center',
      fixed: 'right',
    },
  ];
}

export function useSyncFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'ApiSelect',
      fieldName: 'scenicId',
      label: '同步景区',
      componentProps: {
        // 菜单接口转options格式
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.scenicName,
            value: item.id,
          }));
        },
        // 菜单接口
        api: getAllScenicList,
        placeholder: '请选择需要同步的景区',
        allowClear: true,
      },
    },
  ];
}

export function useBindFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'ApiSelect',
      fieldName: 'ticketId',
      label: '门票',
      componentProps: {
        // 菜单接口转options格式
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.ticketName,
            value: item.id,
          }));
        },
        // 菜单接口
        api: getTicketAllList,
        placeholder: '请选择绑定的门票',
        allowClear: true,
      },
    },
  ];
}

export function useVerifyLogGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'receiptCode',
      label: '验券码',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入验券码',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '状态',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择状态',
        allowClear: true,
        options: [
          { label: '已核销', value: 1 },
          { label: '已撤销', value: 2 },
        ],
      },
    },
    {
      component: 'RangePicker',
      fieldName: 'verificationDate',
      label: '开卡时间',
      hideLabel: true,
      componentProps: {
        format: 'YYYY-MM-DD',
        allowClear: true,
        placeholder: ['核销开始日期', '核销结束日期'],
        separator: '至',
        valueFormat: 'YYYY-MM-DD',
      },
    },
  ];
}

export function useVerifyLogColumns<T = any>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  return [
    {
      title: '验券码',
      field: 'receiptCode',
      width: 120,
      align: 'center',
      fixed: 'left',
    },
    {
      title: '核销数量',
      field: 'count',
      width: 120,
      align: 'center',
    },
    {
      title: '核销时间',
      field: 'createdAt',
      width: 150,
      align: 'center',
    },
    {
      title: '用户手机号',
      field: 'mobile',
      width: 120,
      align: 'center',
    },
    {
      title: '状态',
      field: 'status',
      width: 120,
      align: 'center',
      cellRender: {
        name: 'CellTag',
        options: [
          { value: 1, label: '已核销', color: 'success' },
          { value: 2, label: '已撤销', color: 'warning' },
        ],
      },
    },
    {
      title: '美团订单号',
      field: 'verifyOrderId',
      width: 150,
      align: 'center',
    },
    {
      title: '门票订单号',
      field: 'orderNo',
      width: 180,
      align: 'center',
    },
    {
      title: '核销景区',
      field: 'scenicInfo',
      width: 120,
      align: 'center',
      formatter: ({ row }: any) => {
        return row.scenicInfo?.scenicName;
      },
    },
    {
      title: '核销人',
      field: 'verifyUser',
      width: 120,
      align: 'center',
      formatter: ({ row }: any) => {
        return row.adminUserInfo?.name;
      },
    },
    {
      title: '操作',
      field: 'operation',
      cellRender: {
        attrs: {
          nameField: 'title',
          nameTitle: '验券码',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            text: '撤销',
            code: 'cancel',
            danger: true,
            show: (row: any) => {
              return row.status === 1;
            },
          },
        ],
      },
      fixed: 'right',
      width: 120,
      align: 'center',
    },
  ];
}

<script lang="ts" setup>
import { ref, nextTick } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { Input, message } from 'ant-design-vue';

import payScanCode from '#/assets/payIcon/payScanCode.svg';
defineProps(['totalAmount']);

// ========================================================================================
let payCodeInput: any = ref(null);
let openVal: any = ref({});
const open = async (e: any) => {
  openVal.value = e;
  modalApi.open();
  nextTick(() => {
    payCodeInput.value.focus();
  });
};
defineExpose({ open });

// =========================================================================================
let emits = defineEmits(['cancel']);
const [Modal, modalApi] = useVbenModal({
  destroyOnClose: true,
  fullscreenButton: false,
  header: false,
  closeOnClickModal: false,
  footer: false,
  onOpenChange(isOpen: any) {
    if (!isOpen) {
      emits('cancel', payCode.value);
      payCode.value = '';
    }
  },
});

// =========================================================================================
let payCode = ref<any>('');
const handlePayCode = () => {
  if (!payCode.value.trim()) {
    message.warning('请扫描二维码');
    return;
  }
  modalApi.close();
};
</script>

<template>
  <Modal class="w-[600px]">
    <div class="pt-10">
      <div class="text-center text-2xl">
        请扫客户付款码，支付
        <span class="text-color-error">{{ totalAmount }}</span> 元
      </div>
      <div class="text-color-info mt-3 text-center">
        支持微信、支付宝、云闪付
      </div>
      <div class="flex-center pt-5">
        <img :src="payScanCode" class="w-[300px]" />
      </div>
      <div class="p-10 pb-5">
        <Input
          ref="payCodeInput"
          v-model:value="payCode"
          placeholder="请扫客户付款码"
          @pressEnter="handlePayCode"
          class="w-full"
        />
      </div>
    </div>
  </Modal>
</template>

<style lang="scss" scoped></style>

<script setup lang="ts">
import { ref } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { getMeituanUnauthUrl } from '#/api/manageModule';

const emits = defineEmits(['close']);
let iframeUrl = ref('');
const [Modal, modalApi] = useVbenModal({
  title: '美团解绑',
  showCancelButton: false,
  confirmText: '关闭',
  async onConfirm() {
    modalApi.close();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData();
      getMeituanUnauthUrl(data).then((res) => {
        iframeUrl.value = res;
      });
    } else {
      emits('close');
    }
  },
});
</script>
<template>
  <Modal class="w-[1200px]">
    <iframe
      :src="iframeUrl"
      frameborder="0"
      width="100%"
      height="800px"
    ></iframe>
  </Modal>
</template>

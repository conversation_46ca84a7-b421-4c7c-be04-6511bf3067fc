<script setup lang="ts">
import { ref, toRefs } from 'vue';
import { Page, useVbenModal } from '@vben/common-ui';
import {
  RadioGroup,
  Radio,
  Form,
  Select,
  Input,
  Button,
  Card,
  Descriptions,
  DescriptionsItem,
  Table,
  Modal,
  InputNumber,
  message,
} from 'ant-design-vue';
import type { TableColumnType } from 'ant-design-vue';
import {
  getAllScenicList,
  receiptPrepare,
  consumeMeituanTuangou,
} from '#/api/manageModule';
import { useAccessStore } from '@vben/stores';
const { accessAllEnums } = toRefs(useAccessStore());
import MtVerifyLog from './modules/mtVerifyLog.vue';
import { print } from '#/utils/print';

const currentType = ref<any>(1);
const searchParams = ref<any>({
  scenicId: 1,
  receiptCode: '7290736505',
});
const tuangouInfo = ref<any>({});
const handleReset = () => {
  searchParams.value = {
    scenicId: undefined,
    receiptCode: undefined,
  };
};
const handleSearch = async () => {
  const res = await receiptPrepare(searchParams.value);
  tuangouInfo.value = res;
};

const isOutTicket = ref(true);
const showVerifyModal = ref(false);
const count = ref(1);
const handleVerify = () => {
  showVerifyModal.value = true;
};

const handleVerifyOk = async () => {
  try {
    await consumeMeituanTuangou({
      count: count.value,
      scenicId: searchParams.value.scenicId,
      receiptCode: searchParams.value.receiptCode,
    });
    message.success('核销成功');
    showVerifyModal.value = false;
  } catch (e: any) {
    Modal.error({
      title: '核销失败',
      content: e.message,
    });
  }
};

const [MtVerifyLogModal, mtVerifyLogModalApi] = useVbenModal({
  connectedComponent: MtVerifyLog,
  destroyOnClose: true,
});
const handleVerifyLog = () => {
  mtVerifyLogModalApi.setData({}).open();
};

const scenicList = ref<any>([]);
const getScenicList = async () => {
  const res = await getAllScenicList({});
  scenicList.value = res.map((item: any) => {
    return {
      label: item.scenicName,
      value: item.id,
    };
  });
};
getScenicList();

// 卡类
const cardColumns: TableColumnType[] = [
  {
    title: '门票名称',
    dataIndex: 'ticketName',
    align: 'center',
    width: '50%',
  },
  {
    title: '使用次数',
    dataIndex: 'limit',
    align: 'center',
    width: '50%',
  },
];

const bizTypeMap: any = {
  0: '普通团购',
  203: '拼团',
  205: '次卡',
  217: '通兑标品',
};

const filterText = (arr: any, val: any) => {
  return arr.find((item: any) => item.value === val)?.label;
};
const filterWeek = (arr: any, val: any) => {
  //val为数组，arr为数组，返回val中对应的label
  return val
    .map((item: any) => {
      return arr.find((i: any) => i.value === item)?.label;
    })
    .join('，');
};
</script>
<template>
  <Page auto-content-height>
    <div class="bg-card h-full rounded-lg p-2">
      <div class="pb-2">
        <RadioGroup
          button-style="solid"
          optionType="button"
          v-model:value="currentType"
        >
          <Radio :value="1">美团团购</Radio>
          <Radio :value="2">抖音团购</Radio>
        </RadioGroup>
      </div>
      <div>
        <Form layout="inline" :model="searchParams" class="w-full">
          <div
            class="grid w-full grid-cols-4 gap-2 md:grid-cols-2 xl:grid-cols-4 2xl:grid-cols-5"
          >
            <div class="w-full pb-2">
              <Select
                v-model:value="searchParams.scenicId"
                placeholder="请选择景区"
                class="w-full"
                allowClear
                :options="scenicList"
              >
              </Select>
            </div>
            <div class="w-full pb-2">
              <Input
                v-model:value="searchParams.receiptCode"
                placeholder="请输入美团核销码"
                class="w-full"
                allowClear
              ></Input>
            </div>
            <div class="w-full pb-2 text-right">
              <Button @click="handleReset">重置</Button>
              <Button type="primary" class="ml-2" @click="handleSearch"
                >查询</Button
              >
              <Button type="primary" class="ml-2" @click="handleVerifyLog"
                >查询核销记录</Button
              >
            </div>
          </div>
        </Form>
        <Card title="团购信息" class="mb-2" v-if="tuangouInfo?.tuangouInfo">
          <Descriptions>
            <DescriptionsItem label="团购ID">{{
              tuangouInfo.tuangouInfo?.dealGroupId
            }}</DescriptionsItem>
            <DescriptionsItem label="商品名称">{{
              tuangouInfo.tuangouInfo?.dealTitle
            }}</DescriptionsItem>
            <DescriptionsItem label="商品类型">{{
              tuangouInfo.tuangouInfo?.dealType == 0
                ? '普通团购'
                : '支持在线付尾款'
            }}</DescriptionsItem>
            <DescriptionsItem label="业务类型">{{
              bizTypeMap[tuangouInfo.tuangouInfo?.bizType]
            }}</DescriptionsItem>
            <DescriptionsItem label="销售价">{{
              tuangouInfo.tuangouInfo?.dealPrice
            }}</DescriptionsItem>
            <DescriptionsItem label="市场价">{{
              tuangouInfo.tuangouInfo?.dealMarketPrice
            }}</DescriptionsItem>
            <DescriptionsItem label="支付金额">{{
              tuangouInfo.tuangouInfo?.paymentDetail[0].amount
            }}</DescriptionsItem>
            <DescriptionsItem label="用户手机号">{{
              tuangouInfo.tuangouInfo?.mobile
            }}</DescriptionsItem>
            <DescriptionsItem label="可验证数量">{{
              tuangouInfo.tuangouInfo?.count
            }}</DescriptionsItem>
            <DescriptionsItem label="过期时间">{{
              tuangouInfo.tuangouInfo?.receiptEndDate
            }}</DescriptionsItem>
            <template v-if="tuangouInfo.tuangouInfo?.tgTimesCardFlag">
              <DescriptionsItem label="是否团购次卡">{{
                tuangouInfo.tuangouInfo?.tgTimesCardFlag ? '是' : '否'
              }}</DescriptionsItem>
              <DescriptionsItem label="团购次卡总次数">{{
                tuangouInfo.tuangouInfo?.purchaseToConsumeRatio
              }}</DescriptionsItem>
            </template>
          </Descriptions>
        </Card>
        <Card title="门票信息" class="mb-2" v-if="tuangouInfo?.ticketInfo">
          <Descriptions>
            <DescriptionsItem label="门票名称">{{
              tuangouInfo.ticketInfo?.ticketName
            }}</DescriptionsItem>
            <DescriptionsItem label="门票类型">{{
              filterText(
                accessAllEnums.ticketModel.list,
                tuangouInfo.ticketInfo?.model,
              )
            }}</DescriptionsItem>
            <DescriptionsItem label="不可用日期">{{
              tuangouInfo.ticketInfo?.unavailableDateList.join('，')
            }}</DescriptionsItem>
            <DescriptionsItem label="不可用节假日">{{
              tuangouInfo.ticketInfo?.unavailableHolidayList
                .map((item: any) => item.holidayName)
                .join('，')
            }}</DescriptionsItem>
            <DescriptionsItem label="适用星期">{{
              filterWeek(
                accessAllEnums.ticketApplicablePeriod.list,
                tuangouInfo.ticketInfo?.applicablePeriod,
              )
            }}</DescriptionsItem>
            <DescriptionsItem label=""></DescriptionsItem>
            <!--套票类 -->
            <DescriptionsItem
              label="包含子票"
              :span="3"
              v-if="tuangouInfo.ticketInfo?.model == 2"
              >{{
                tuangouInfo.ticketInfo?.childList
                  .map((item: any) => item.ticketInfo.ticketName)
                  .join('，')
              }}</DescriptionsItem
            >
          </Descriptions>
          <!-- 卡类 -->
          <Table
            :columns="cardColumns"
            :pagination="false"
            :data-source="tuangouInfo.ticketInfo?.childList"
            v-if="[3, 4, 5].includes(tuangouInfo.ticketInfo?.model)"
            class="mb-5"
            bordered
          >
            <template #bodyCell="{ column, text, record }">
              <template v-if="column.dataIndex == 'ticketName'">
                <div class="text-ellipsis">
                  {{ record.ticketInfo.ticketName }}
                </div>
              </template>
              <template v-if="column.dataIndex == 'limit'">
                <p v-if="record.isLimit == 0">不限次数</p>
                <p v-else>
                  共{{ record.totalLimit }}次，每月最多{{
                    record.monthLimit
                  }}次，每日最多{{ record.dayLimit }}次
                </p>
              </template>
            </template>
          </Table>
        </Card>
        <div class="text-right" v-if="tuangouInfo?.tuangouInfo">
          <Button type="primary" @click="handleVerify">核 销</Button>
        </div>
      </div>
    </div>
    <!-- 核销弹窗 -->
    <Modal
      v-model:open="showVerifyModal"
      :title="'核销--' + tuangouInfo.tuangouInfo?.dealTitle"
      ok-text="确定核销"
      @ok="handleVerifyOk"
    >
      <div class="py-5">
        <div class="flex items-center gap-2">
          <span>核销数量</span>
          <InputNumber
            v-model:value="count"
            :precision="0"
            :min="1"
            :max="tuangouInfo.tuangouInfo?.count"
          />
        </div>
        <div class="mt-3 flex items-center gap-2">
          <span>是否出票</span>
          <RadioGroup
            v-model:value="isOutTicket"
            :options="[
              { label: '是', value: true },
              { label: '否', value: false },
            ]"
          ></RadioGroup>
        </div>
      </div>
    </Modal>
    <MtVerifyLogModal></MtVerifyLogModal>
  </Page>
</template>

<script lang="ts" setup>
import eventBus from '#/utils/eventBus';
import { ref } from 'vue';
import { DeleteOutlined } from '@ant-design/icons-vue';
import {
  InputSearch,
  Space,
  DatePicker,
  Tabs,
  TabPane,
  Button,
} from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';

import { confirm } from '@vben/common-ui';

import { ticketTypeList } from '#/api/windowModule/ticketSales';

// =======================================================================================
const disabledDate = (current: Dayjs) => {
  return (
    (current && current < dayjs().startOf('day')) ||
    current > dayjs().add(1, 'year').startOf('day')
  );
};
let currentDate: any = ref(dayjs());
const searchTime = () => {
  confirm('切换时间后，已选门票将会全部清空，是否确认继续？')
    .then(() => {
      getListParams.value.playDate = currentDate.value.format('YYYY-MM-DD');
      clearAllData();
    })
    .catch(() => {
      currentDate.value = dayjs(getListParams.value.playDate) || dayjs();
    });
};

// ========================================================================================
import leftTem from './left/index.vue';
let leftTemRef: any = ref(null);

let getListParams = ref({
  name: '',
  playDate: currentDate.value.format('YYYY-MM-DD'),
  typeId: 0,
});
const getList = () => {
  leftTemRef.value.getList();
};

// ========================================================================================
import rightTem from './right/index.vue';
let rightTemRef: any = ref(null);

// ========================================================================================
let typeList: any = ref([]);
const getTypeList = async () => {
  let resData = await ticketTypeList({});
  typeList.value = resData;
  let params = { id: 0, typeName: '全部' };
  typeList.value.unshift(params);
};
const typeBtn = (e: any) => {
  getListParams.value.typeId = e;
  getList();
};
// ========================================================================================
const getInfoData = async () => {
  await getTypeList();
  await getList();
};
getInfoData();

// ========================================================================================
const clearBtn = () => {
  confirm('清空后所选数据将全部清空，是否确认继续？')
    .then(async () => {
      clearAllData();
    })
    .catch(() => {});
};
const clearAllData = async () => {
  await leftTemRef.value.clearBtn();
  await rightTemRef.value.clearBtn();
  await getList();
};
eventBus.on('ticketSalesClearAllData', () => {
  clearAllData();
});

const submitBtn = () => {
  rightTemRef.value.placeOrderBtn();
};
</script>

<template>
  <div class="flex h-full min-h-full w-full justify-between gap-4">
    <!-- left -->
    <div class="bg-color-white flex w-2/5 flex-col rounded-lg pt-4">
      <Space direction="vertical" class="w-full px-4">
        <div class="flex w-full items-center justify-between gap-4">
          <InputSearch
            v-model:value="getListParams.name"
            placeholder="请输入门票名称"
            size="large"
            allowClear
            @search="getList"
          />
          <DatePicker
            v-model:value="currentDate"
            :disabled-date="disabledDate"
            format="YYYY-MM-DD"
            :allowClear="false"
            placeholder="请选择日期"
            size="large"
            class="w-full"
            @change="searchTime"
          />
        </div>

        <Tabs v-model:activeKey="getListParams.typeId" @change="typeBtn">
          <TabPane
            v-for="item in typeList"
            :key="item.id"
            :tab="item.typeName"
          />
        </Tabs>
      </Space>

      <div class="flex-1 overflow-hidden overflow-y-auto px-4">
        <leftTem ref="leftTemRef" :getListParams="getListParams" />
      </div>
    </div>

    <!-- right -->
    <div class="bg-color-white flex flex-1 flex-col rounded-lg">
      <div class="flex items-center justify-between border-b px-4 py-2">
        <div class="text-lg">已选门票</div>
        <div
          class="flex-center text-color-info cursor-pointer gap-1"
          @click="clearBtn"
        >
          <DeleteOutlined />
          <div>清空</div>
        </div>
      </div>
      <div class="flex-1 overflow-hidden">
        <rightTem ref="rightTemRef" :searchInfo="getListParams">
          <template #footer>
            <Space>
              <Button>挂单</Button>
              <Button type="primary" @click="submitBtn">下单</Button>
            </Space>
          </template>
        </rightTem>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>

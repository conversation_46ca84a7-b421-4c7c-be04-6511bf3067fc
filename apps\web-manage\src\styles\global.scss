// 置灰
.setGreyOut {
  filter: grayscale(1);
}

// 文本颜色
.text-color-white {
  color: #fff;
}
.text-color-black {
  color: #000;
}
.text-color-primary {
  color: hsl(var(--primary));
}
.text-color-success {
  color: hsl(var(--success));
}
.text-color-warning {
  color: hsl(var(--warning));
}
.text-color-error {
  color: hsl(var(--destructive));
}
.text-color-info {
  color: hsl(var(--muted-foreground));
}

// 背景颜色
.bg-color-white {
  background-color: hsl(var(--header));
}
.bg-color-black {
  background-color: #000;
}
.bg-color-quickBox {
  background-color: hsl(var(--background-deep));
}
.bg-color-primary {
  background-color: hsl(var(--primary));
}
.bg-color-success {
  background-color: hsl(var(--success));
}
.bg-color-warning {
  background-color: hsl(var(--warning));
}
.bg-color-error {
  background-color: hsl(var(--destructive));
}
.bg-color-info {
  background-color: hsl(var(--muted-foreground));
}

// 边框颜色border
.border-color-primary {
  border-color: hsl(var(--primary));
}
.border-color-success {
  border-color: hsl(var(--success));
}
.border-color-warning {
  border-color: hsl(var(--warning));
}
.border-color-error {
  border-color: hsl(var(--destructive));
}
.border-color-info {
  border-color: hsl(var(--muted-foreground));
}

// 设置超出显示省略号
@for $i from 1 through 10 {
  .ellipsis-#{$i} {
    display: -webkit-box !important;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
    -webkit-line-clamp: #{$i};
    -webkit-box-orient: vertical !important;
  }
}

// 全局loading css样式
.myFullSpin {
  width: 100%;
  height: 100%;
  z-index: 3000;
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0;
  > div {
    position: absolute;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.2);
    z-index: 1;
  }
  .ant-spin-dot {
    position: absolute;
    display: inline-block;
    font-size: 20px;
    width: 1em;
    height: 1em;
    z-index: 2;
    > i {
      position: absolute;
      display: block;
      width: 9px;
      height: 9px;
      background-color: #1677ff;
      border-radius: 100%;
      transform: scale(0.75);
      transform-origin: 50% 50%;
      opacity: 0.3;
      animation-name: css-1p3hq3p-antSpinMove;
      animation-duration: 1s;
      animation-iteration-count: infinite;
      animation-timing-function: linear;
      animation-direction: alternate;
      &:nth-child(1) {
        top: 0;
        inset-inline-start: 0;
      }
      &:nth-child(2) {
        top: 0;
        inset-inline-end: 0;
        animation-delay: 0.4s;
      }
      &:nth-child(3) {
        inset-inline-end: 0;
        bottom: 0;
        animation-delay: 0.8s;
      }
      &:nth-child(4) {
        bottom: 0;
        inset-inline-start: 0;
        animation-delay: 1.2s;
      }
    }
  }
}

<script lang="ts" setup>
import eventBus from '#/utils/eventBus';
import { ref, computed } from 'vue';

import { message, InputNumber, Space, Empty } from 'ant-design-vue';

let props = defineProps(['searchInfo']);

import { useAccessStore } from '@vben/stores';
const { accessAllEnumsMap } = useAccessStore();

// ==========================================================================
import listTimeTem from './listTime.vue';
import listIdCarTem from './listIdCar.vue';
import placeOrderTem from './placeOrder.vue';
let placeOrderTemRef: any = ref(null);

// ========================================================================================
import math from '#/utils/math';
const totalAmount = computed(() => {
  return selectAll.value.reduce((acc: any, cur: any) => {
    return math.add(acc, math.multiply(cur.sellingPrice, cur.ticketNum));
  }, 0);
});

// ==========================================================================
import { ticketInfo } from '#/api/windowModule/ticketSales';
let selectAll: any = ref([]);
const getInfo = async (e: any) => {
  let resData: any = null;

  let index = selectAll.value.findIndex((item: any) => item.id === e.ticketId);
  if (index === -1) {
    let getParams = {
      ticketId: e.ticketId,
      playDate: props.searchInfo.playDate,
    };
    resData = await ticketInfo(getParams);

    let params = {
      ...resData,
      ticketNum: 1,
    };
    selectAll.value.unshift(params);
  } else {
    let obj = selectAll.value[index];
    if (
      !obj.isOrderQuantity ||
      selectAll.value[index].ticketNum < obj.orderQuantity
    ) {
      selectAll.value[index].ticketNum++;
    } else {
      message.warning(`限购${obj.orderQuantity}张`);
    }
    return;
  }

  eventBus.emit('ticketSalesListRefreshData', resData);

  // 分时预约，没有可预约的时间段
  if (resData.isReservation && !resData.periodList.length) {
    message.error('当前门票为分时预约票，当天已无可约场次！');
  }
};

// ========================================================================================================================
eventBus.on('ticketSalesAddSelect', (e: any) => {
  getInfo(e);
});

const clearBtn = () => {
  selectAll.value = [];
};
const placeOrderBtn = () => {
  if (!selectAll.value.length) {
    message.error('请选择门票！');
    return;
  }

  let errStr = '';
  for (let i = 0; i < selectAll.value.length; i++) {
    let item = selectAll.value[i];
    if (!errStr) {
      if (item.periodList && item.periodList.length && !item.periodId) {
        errStr += `${item.ticketName} 请选择场次！`;
        break;
      }
      if (item.authenticationType > 1) {
        for (let i = 0; i < item.touristList.length; i++) {
          let ite = item.touristList[i];
          if (ite.name === '' || ite.idcard === '') {
            errStr += `${item.ticketName} 请填写游客信息！`;
            break;
          }
        }
      }
    }
  }
  if (errStr) {
    message.error(errStr);
    return;
  }

  placeOrderTemRef.value.open(selectAll.value);
};
defineExpose({ clearBtn, placeOrderBtn });

const deleteBtn = (e: any, index: number) => {
  selectAll.value.splice(index, 1);
  eventBus.emit('ticketSalesListLeftSelectDel', e);
};
</script>

<template>
  <div class="flex h-full flex-col">
    <div class="flex-1 overflow-y-auto p-4">
      <div
        v-for="(item, index) in selectAll"
        :key="item.id"
        class="overflow-hidden rounded-lg border"
        :class="index ? 'mt-4' : ''"
      >
        <div class="bg-color-quickBox flex items-center p-2">
          <div class="flex-1">门票</div>
          <div class="min-w-[110px]">门票类型</div>
          <div class="min-w-[110px]">数量</div>
          <div class="min-w-[110px]">价格</div>
          <div class="min-w-[110px]">二维码类型</div>
          <div class="min-w-[30px]">操作</div>
        </div>
        <div class="flex items-center p-2">
          <div class="flex-1">
            <div class="ellipsis-1">{{ item.ticketName }}</div>
          </div>
          <div class="min-w-[110px]">
            {{ accessAllEnumsMap.ticketModel[item.model] }}
          </div>
          <div class="min-w-[110px]">
            <InputNumber
              id="inputNumber"
              v-model:value="item.ticketNum"
              placeholder="请输入数量"
              :min="1"
              :max="!item.isOrderQuantity ? 9999 : item.orderQuantity"
            />
          </div>
          <div class="min-w-[110px]">
            <div v-if="item.isModifyPrice === 1">
              <InputNumber
                id="inputNumber"
                v-model:value="item.sellingPrice"
                placeholder="请输入价格"
                :min="0.01"
                :precision="2"
                string-mode
              />
            </div>
            <div v-else>{{ item.sellingPrice }}</div>
          </div>
          <div class="min-w-[110px]">
            {{ accessAllEnumsMap.tickeQrcodeRule[item.qrcodeRule] }}
          </div>
          <div class="w-[30px]">
            <div
              class="text-color-error cursor-pointer"
              @click="deleteBtn(item, index)"
            >
              删除
            </div>
          </div>
        </div>

        <div
          class="flex w-full border-t p-3"
          v-if="
            (item.periodList && item.periodList.length) ||
            item.authenticationType > 1
          "
        >
          <Space direction="vertical" class="w-full">
            <listTimeTem
              v-model:value="item.periodId"
              v-if="item.periodList && item.periodList.length"
              :list="item.periodList"
              :searchInfo="searchInfo"
            />
            <listIdCarTem
              v-model:value="item.touristList"
              v-if="item.authenticationType > 1"
              :row="item"
            />
          </Space>
        </div>
      </div>

      <div v-if="!selectAll.length">
        <Empty />
      </div>
    </div>
    <div class="flex items-center justify-between border-t px-4 py-3">
      <div>
        共 <span class="text-color-error">{{ selectAll.length }}</span> 张，合计
        <span class="text-color-error">￥</span>
        <span class="text-color-error text-xl font-bold">
          {{ totalAmount }}
        </span>
        元
      </div>
      <slot name="footer" />
    </div>
  </div>

  <placeOrderTem ref="placeOrderTemRef" :searchInfo="searchInfo" />
</template>

<style scoped lang="scss"></style>

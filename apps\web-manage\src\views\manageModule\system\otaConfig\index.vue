<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { Page, useVbenModal } from '@vben/common-ui';
import { InputSearch, Table, Image, Button, Tag, Spin } from 'ant-design-vue';
import { IconifyIcon } from '@vben/icons';
import {
  getAllScenicList,
  getMeituanConfig,
  getMeituanAuthUrl,
  getMeituanUnauthUrl,
} from '#/api/manageModule';
import meituan from '#/assets/images/meituan.png';
import douyin from '#/assets/images/douyin.png';
import { router } from '#/router';
import MeituanUnAuthorize from './meituanUnAuthorize.vue';

const searchValue = ref<any>(undefined);
const scenicList = ref<any>([]);
const selectedRowKey = ref<any>(null); // 选中的行key
const selectedRow = ref<any>({});

const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
  },
  {
    title: '景区名称',
    dataIndex: 'scenicName',
  },
];

const rightList = ref([
  {
    title: '美团团购',
    key: 'mtTg',
    icon: meituan,
    description: '需先开通美团商户通道，然后进行授权绑定',
    isBind: false,
    bindName: '',
  },
  {
    title: '美团门票',
    key: 'mtMp',
    icon: meituan,
    description: '需先开通美团商户通道，然后进行授权绑定',
    isBind: false,
    bindName: '',
  },
  {
    title: '抖音团购',
    key: 'dyTg',
    icon: douyin,
    description: '需先开通抖音商户通道，然后进行授权绑定',
    isBind: false,
    bindName: '',
  },
  {
    title: '抖音门票',
    key: 'dyMp',
    icon: douyin,
    description: '需先开通抖音商户通道，然后进行授权绑定',
    isBind: false,
    bindName: '',
  },
]);

const onSearch = (val: string) => {
  console.log(val);
  getScenicList();
};

// 处理行点击事件
const onRowClick = (record: any) => {
  selectedRowKey.value = record.id;
  selectedRow.value = record; // 选中行数据更新为当前行数据
  console.log('选中行:', record);
  getScenicMtConfig();
};

const getScenicList = async () => {
  const res = await getAllScenicList({ name: searchValue.value });
  scenicList.value = res;
  // 默认选中第一行
  selectedRowKey.value = res[0]?.id;
  selectedRow.value = res[0];
  getScenicMtConfig();
};
onMounted(() => {
  getScenicList();
  
  //将方法挂载到 window 对象上，供子页面调用
  (window as any).parentMethod = () => {
    getScenicMtConfig();
  };
});

async function getScenicMtConfig() {
  const res = await getMeituanConfig({
    scenicId: selectedRowKey.value,
    businessId: 58,
  });
  if (res) {
    rightList.value.map((item) => {
      if (item.key == 'mtTg') {
        item.bindName = res.opBizName;
        item.isBind = true;
      }
      return item;
    });
  } else {
    rightList.value.map((item) => {
      if (item.key == 'mtTg') {
        item.isBind = false;
      }
      return item;
    });
  }
}

const getAuthorizeUrl = async () => {
  const res = await getMeituanAuthUrl({
    scenicId: selectedRow.value.id,
    businessId: 58,
  });

  window.open(
    res,
    // 'http://localhost:5666/#/manage/system/meituanAuthorize',
    'authorizeWindow',
    'height=850,width=1200,top=100,left=200,toolbar=yes,menubar=yes,location=yes, status=no',
  );
};
const loading = ref(false);

const [UnauthorizeModal, unauthorizeModalApi] = useVbenModal({
  connectedComponent: MeituanUnAuthorize,
  destroyOnClose: true,
});
const getUnauthorizeUrl = async () => {
  unauthorizeModalApi
    .setData({
      scenicId: selectedRow.value.id,
      businessId: 58,
    })
    .open();
};

const onClose = () => {
  loading.value = true;
  setTimeout(() => {
    getScenicMtConfig();
    loading.value = false;
  }, 1000);
};
</script>
<template>
  <Page auto-content-height>
    <Spin :spinning="loading" tip="配置获取中...">
      <div class="flex h-full gap-5">
        <div class="bg-card rounded-lg p-2">
          <InputSearch
            v-model:value="searchValue"
            placeholder="请输入景区名称"
            class="w-[300px]"
            allowClear
            @search="onSearch"
          />
          <Table
            class="mt-2"
            :columns="columns"
            :data-source="scenicList"
            :pagination="false"
            :row-key="(record) => record.id"
            :row-class-name="
              (record) => (record.id === selectedRowKey ? 'selected-row' : '')
            "
            :custom-row="
              (record) => ({
                onClick: () => onRowClick(record),
              })
            "
          ></Table>
        </div>
        <div>
          <div v-for="item in rightList">
            <div class="bg-card mb-5 rounded-lg p-3">
              <div class="flex gap-5">
                <Image
                  :src="item.icon"
                  :preview="false"
                  width="64px"
                  height="64px"
                />
                <div>
                  <div class="flex items-center gap-2">
                    <h3 class="mb-1 text-lg font-bold">{{ item.title }}</h3>
                    <Tag :color="item.isBind ? 'processing' : 'warning'">{{
                      item.isBind ? '已授权' : '未授权'
                    }}</Tag>
                  </div>
                  <p class="text-sm opacity-50">{{ item.description }}</p>
                </div>
              </div>
              <div class="mt-5 flex items-center justify-between text-right">
                <p v-if="item.isBind">商户名称：{{ item.bindName }}</p>
                <p v-else></p>
                <Button
                  type="primary"
                  v-if="!item.isBind"
                  :disabled="item.key !== 'mtTg'"
                  @click="getAuthorizeUrl"
                  >去授权</Button
                >
                <Button
                  type="primary"
                  danger
                  :disabled="item.key !== 'mtTg'"
                  @click="getUnauthorizeUrl"
                  v-else
                  >解除授权</Button
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </Spin>
    <UnauthorizeModal @close="onClose"></UnauthorizeModal>
  </Page>
</template>

<style scoped>
/* 选中行的高亮样式 */
:deep(.selected-row) {
  background-color: #e6f7ff !important;
}

/* 鼠标悬停效果 */
:deep(.ant-table-tbody > tr:hover) {
  background-color: #f5f5f5 !important;
  cursor: pointer;
}

/* 选中行在悬停时保持高亮 */
:deep(.selected-row:hover) {
  background-color: #bae7ff !important;
}
</style>

import { requestClient } from '#/api/request';

// 切换售票点
export const changeOffice = (params: any) => {
  return requestClient.post('/win/adminUser/changeOffice', params);
};

// 获取我的信息
export const getMyInfo = (params: any) => {
  return requestClient.get('/sys/adminUser/getMyInfo', params);
};

// 更新我的信息
export const updateAdminUser = (params: any) => {
  return requestClient.post('/sys/adminUser/updateMyInfo', params);
}

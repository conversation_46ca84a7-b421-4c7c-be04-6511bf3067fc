// 重命名类以避免与全局对象 sessionStorage 冲突
class SessionStorage {
  // 清除所有数据
  clear(): void {
    try {
      sessionStorage.clear();
    } catch (error) {
      console.error('SessionStorageUtil clear error:', error);
    }
  }

  // 获取指定键的数据
  get<T = any>(key: string): null | T {
    try {
      const item = sessionStorage.getItem(key);
      return item ? JSON.parse(item) : null;
    } catch (error) {
      console.error('SessionStorageUtil getItem error:', { key, error });
      return null;
    }
  }

  // 移除指定键的数据
  remove(key: string): void {
    try {
      sessionStorage.removeItem(key);
    } catch (error) {
      console.error('SessionStorageUtil removeItem error:', { key, error });
    }
  }

  // 设置数据到指定键
  set(key: string, value: any): void {
    try {
      sessionStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error('SessionStorageUtil setItem error:', { key, value, error });
    }
  }
}

export default new SessionStorage();

import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

async function getMeituanTuangouList(params: Recordable<any>) {
  return requestClient.get('/tkt/ota/meituan/tuangou/list', { params });
}

async function refreshMeituanTuangou(data: Recordable<any>) {
  return requestClient.post('/tkt/ota/meituan/tuangou/refresh', data);
}
async function bindMeituanTuangou(data: Recordable<any>) {
  return requestClient.post('/tkt/ota/meituan/tuangou/bind', data);
}
async function unbindMeituanTuangou(data: Recordable<any>) {
  return requestClient.post('/tkt/ota/meituan/tuangou/unbind', data);
}

export {
  getMeituanTuangouList,
  refreshMeituanTuangou,
  bindMeituanTuangou,
  unbindMeituanTuangou,
};

import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import { useAccessStore } from '@vben/stores';
import { toRefs } from 'vue';
const { accessAllEnums } = toRefs(useAccessStore());
import type { TableColumnType } from 'ant-design-vue';
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'goodsName',
      label: '物品名称',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入物品名称',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'orderNo',
      label: '订单号',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入订单号',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'verificationCode',
      label: '凭证码',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入凭证码',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'orderSource',
      label: '订单来源',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择订单来源',
        allowClear: true,
        options: accessAllEnums.value?.orderSource.list,
      },
    },
    {
      component: 'Select',
      fieldName: 'payMethod',
      label: '支付方式',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择支付方式',
        allowClear: true,
        options: accessAllEnums.value?.orderPayMethod.list,
      },
    },
    {
      component: 'Select',
      fieldName: 'payStatus',
      label: '付款状态',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择付款状态',
        allowClear: true,
        options: accessAllEnums.value?.orderPayStatus.list,
      },
    },
    {
      component: 'Select',
      fieldName: 'orderStatus',
      label: '订单状态',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择订单状态',
        allowClear: true,
        options: accessAllEnums.value?.rentalOrderStatus.list,
      },
    },
    {
      component: 'RangePicker',
      fieldName: 'orderDate',
      label: '下单日期',
      hideLabel: true,
      componentProps: {
        format: 'YYYY-MM-DD',
        allowClear: true,
        placeholder: ['下单开始日期', '下单结束日期'],
        separator: '至',
        valueFormat: 'YYYY-MM-DD',
      },
    },
  ];
}

export function useColumns<T = any>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  return [
    {
      type: 'expand',
      fixed: 'left',
      headerAlign: 'center',
      width: 30,
      slots: { content: 'expand_content' },
    },
    {
      field: 'goodsName',
      title: '物品信息',
      minWidth: 400,
      fixed: 'left',
      headerAlign: 'center',
      align: 'left',
      slots: {
        default: 'expand_header',
      },
    },
    {
      field: 'billingMethod',
      title: '计费方式',
      width: 120,
      formatter: ({ row }: any) => {
        return row.isParent ? '' : row.billingMethod;
      },
    },
    {
      field: 'rentalPrice',
      title: '租金',
      width: 120,
      formatter: ({ row }: any) => {
        return row.isParent ? '' : row.rentalPrice;
      },
    },
    {
      field: 'depositPrice',
      title: '租金',
      width: 120,
      formatter: ({ row }: any) => {
        return row.isParent ? '' : row.depositPrice;
      },
    },
    {
      field: 'rentalNum',
      title: '租赁数量',
      width: 120,
      formatter: ({ row }: any) => {
        return row.isParent ? '' : row.rentalNum;
      },
    },
    {
      field: 'orderTime',
      title: '下单时间',
      width: 180,
      formatter: ({ row }: any) => {
        return row.isParent ? '' : row.orderTime;
      },
    },
    {
      field: 'damagePrice',
      title: '损坏补费',
      width: 120,
    },
    {
      field: 'rentalPriceTotal',
      title: '租金总额',
      width: 120,
    },
    {
      field: 'depositPriceTotal',
      title: '押金总额',
      width: 120,
    },
    {
      field: 'damagePriceTotal',
      title: '损坏补费总额',
      width: 120,
    },
    {
      field: 'payStatus',
      title: '支付状态',
      width: 180,
    },
    {
      field: 'orderStatus',
      title: '订单状态',
      width: 180,
    },
    {
      field: 'payMethod',
      title: '支付方式',
      width: 180,
    },
    {
      align: 'center',
      field: 'operation',
      fixed: 'right',
      title: '操作',
      width: 120,
    },
  ];
}

export function useLogTableSchema(): TableColumnType[] {
  return [
    {
      title: '操作内容',
      dataIndex: 'actionContent',
      minWidth: 150,
      align: 'center',
    },
    {
      title: '订单状态',
      dataIndex: 'orderStatus',
      width: 150,
      customRender: ({ record }: any) => {
        return accessAllEnums.value?.rentalOrderStatus.list.find(
          (item: any) => item.value === record.orderStatus,
        )?.label;
      },
      align: 'center',
    },
    {
      title: '下单人',
      dataIndex: 'userInfo',
      width: 150,
      customRender: ({ record }: any) => {
        return record.userInfo?.name || '--';
      },
      align: 'center',
    },
    {
      title: '游客名称',
      dataIndex: 'touristName',
      width: 180,
      align: 'center',
    },
    {
      title: '操作管理员',
      dataIndex: 'ticketName',
      width: 150,
      align: 'center',
    },
    {
      title: '操作时间',
      dataIndex: 'createdAt',
      width: 180,
      align: 'center',
    },
  ];
}

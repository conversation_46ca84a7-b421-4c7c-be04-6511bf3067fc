import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import { getAllScenicList } from '#/api/manageModule';

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'name',
      label: '类型名称',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入类型名称',
        allowClear: true,
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'scenicId',
      label: '所属景区',
      hideLabel: true,
      componentProps: {
        // 菜单接口转options格式
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.scenicName,
            value: item.id,
          }));
        },
        // 菜单接口
        api: getAllScenicList,
        placeholder: '请选择所属景区',
        allowClear: true,
      },
    },
  ];
}

export function useColumns<T = any>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'typeName',
      title: '分类名称',
    },
    {
      field: 'scenicInfo',
      title: '所属景区',
      formatter: ({ row }: any) => {
        return row.scenicInfo?.scenicName;
      },
    },
    {
      field: 'createdAt',
      title: '创建时间',
    },
    {
      align: 'center',
      field: 'status',
      title: '状态',
      cellRender: {
        name: 'CellTag',
      },
      width: 120,
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'typeName',
          nameTitle: '分类名称',
          onClick: onActionClick,
        },
        name: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      title: '操作',
      width: 130,
    },
  ];
}

export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'typeName',
      label: '类型名称',
      rules: 'required',
      componentProps: {
        placeholder: '请输入类型名称',
        allowClear: true,
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'scenicId',
      label: '所属景区',
      componentProps: {
        // 菜单接口转options格式
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.scenicName,
            value: item.id,
          }));
        },
        // 菜单接口
        api: getAllScenicList,
        placeholder: '请选择所属景区',
        allowClear: true,
      },
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: '启用', value: 1 },
          { label: '不启用', value: 0 },
        ],
        optionType: 'default',
      },
      defaultValue: 1,
      fieldName: 'status',
      label: '状态',
    },
    {
      component: 'InputNumber',
      fieldName: 'listorder',
      label: '排序',
      componentProps: {
        placeholder: '请输入排序',
        allowClear: true,
      },
    },
  ];
}

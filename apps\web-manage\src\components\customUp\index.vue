<template>
  <div class="overflow-hidden">
    <div class="w-full overflow-hidden" style="margin: -0.5rem 0 0 -0.5rem">
      <div
        v-for="file in fileList"
        :key="file.uid"
        class="group relative float-left ml-2 mt-2 overflow-hidden rounded-lg"
        :style="{ height: `${size}px`, width: `${size}px` }"
      >
        <div
          class="flex-center text-color-error absolute right-0 top-0 z-10 cursor-pointer p-1 opacity-0 group-hover:opacity-100"
          @click="handleRemove(file)"
        >
          <CloseOutlined />
        </div>
        <div
          v-if="file.status === 'uploading'"
          class="flex-center bg-color-quickBox h-full w-full text-2xl"
        >
          <loading-outlined />
        </div>
        <div v-else class="bg-color-quickBox h-full w-full cursor-pointer">
          <!-- 图片 -->
          <img
            v-if="getMediaType(file.url) === 'image'"
            :src="`${file.url}?x-oss-process=image/resize,w_${size * 2},h_${size * 2},m_fill`"
            class="block h-full w-full object-cover"
            @click="imgPreviewBtn(file)"
          />
          <!-- 视频 -->
          <div
            v-if="getMediaType(file.url) === 'video'"
            class="flex-center relative h-full w-full"
            @click="previewBtn('video', file)"
          >
            <div
              class="flex-center absolute left-0 top-0 z-10 h-full w-full text-2xl"
            >
              <PlayCircleOutlined />
            </div>
            <img
              :src="`${file.url}?x-oss-process=video/snapshot,t_0,f_jpg,w_${size * 2},h_${size * 2}`"
              class="block h-full w-full object-cover"
            />
          </div>

          <!-- 音频 -->
          <div
            v-if="getMediaType(file.url) === 'radio'"
            class="flex-center h-full w-full text-2xl"
            @click="previewBtn('radio', file)"
          >
            <SoundOutlined />
          </div>
          <!-- 其他文件 -->
          <div
            v-if="
              ['excel', 'word', 'pdf', 'ppt'].includes(getMediaType(file.url))
            "
            class="flex-center h-full w-full text-2xl"
            @click="previewBtn('other', file)"
          >
            <FileExcelOutlined v-if="getMediaType(file.url) === 'excel'" />
            <FileWordOutlined v-if="getMediaType(file.url) === 'word'" />
            <FilePdfOutlined v-if="getMediaType(file.url) === 'pdf'" />
            <FilePptOutlined v-if="getMediaType(file.url) === 'ppt'" />
          </div>
        </div>
      </div>

      <div
        v-if="isUp && maxCount && fileList.length < maxCount"
        class="float-left ml-2 mt-2 overflow-hidden"
      >
        <Upload
          :show-upload-list="false"
          name="file"
          :accept="upAccept"
          :multiple="multiple"
          :maxCount="maxCount"
          :custom-request="customRequest"
        >
          <slot v-if="$slots.customUp" name="customUp" />
          <div
            v-else
            class="uploadBox flex-center bg-color-quickBox rounded-lg"
            :style="{ height: `${size}px`, width: `${size}px` }"
          >
            <div class="text-color-info text-2xl">
              <loading-outlined v-if="loading"></loading-outlined>
              <plus-outlined v-else></plus-outlined>
            </div>
          </div>
        </Upload>
      </div>
    </div>
  </div>

  <!-- 图片预览 -->
  <div style="display: none">
    <ImagePreviewGroup
      :preview="{
        visible,
        onVisibleChange: (vis: any) => (visible = vis),
        current: previewIndex,
      }"
    >
      <Image v-for="file in previewList" :key="file.uid" :src="file.url" />
    </ImagePreviewGroup>
  </div>

  <!-- 视频预览 -->
  <Modal class="w-[600px]" title="预览">
    <video
      v-if="previewType === 'video'"
      controls
      autoplay
      style="width: 100%"
      :src="previewFile"
    />
    <audio
      v-if="previewType === 'radio'"
      class="mt-10"
      controls
      autoplay
      style="width: 100%"
      :src="previewFile"
    />
  </Modal>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from 'vue';
import {
  PlusOutlined,
  LoadingOutlined,
  CloseOutlined,
  SoundOutlined,
  PlayCircleOutlined,
  FileExcelOutlined,
  FileWordOutlined,
  FilePdfOutlined,
  FilePptOutlined,
} from '@ant-design/icons-vue';
import { Upload, message, ImagePreviewGroup, Image } from 'ant-design-vue';

import { getMediaType, getMediaUpType } from '#/utils/validate';
import { customOssUpload } from '#/utils/aliyun-oss';

// ===========================================================================
const props = defineProps({
  value: {
    type: Array,
    default: () => [],
  },
  isUp: {
    type: Boolean,
    default: false,
  },
  // 上传文件的类型标签，默认为空（所有） image,video,radio,excel,word,pdf,ppt
  accept: {
    type: String,
    default: '',
  },
  // 上传文件的最大数量，默认为 9
  maxCount: {
    type: Number,
    default: 9,
  },
  // 是否多选
  multiple: {
    type: Boolean,
    default: true,
  },
  // 单个文件的最大体积限制，默认为 10M
  // 图片10M  视频200M 音频50M  文件20M
  maxFileSize: {
    type: Object,
    default: {
      image: 10,
      video: 200,
      radio: 50,
      excel: 20,
      word: 20,
      pdf: 20,
      ppt: 20,
    },
  },
  // 上传文件的类型标签，默认为 fileTag
  fileTypeTag: {
    type: String,
    default: 'fileTag',
  },
  // 上传按钮的尺寸
  size: {
    type: Number,
    default: 80,
  },
});

// ==========================================================================
let upNameList = ref('');
const upAccept = computed(() => {
  let obj = '';
  if (props.accept) {
    let acceptList = props.accept.split(',');
    acceptList.forEach((item, index) => {
      if (index === 0) {
        obj += getMediaUpType(item).fileAccept;
        upNameList.value = getMediaUpType(item).fileName;
      } else {
        obj += ',' + getMediaUpType(item).fileAccept;
        upNameList.value += '、' + getMediaUpType(item).fileName;
      }
    });
  } else {
    obj = '.';
  }
  return obj;
});

// ==========================================================================
const visible = ref(false);
const fileList: any = ref([]);
const loading = ref<boolean>(false);

const setInfo = () => {
  fileList.value = [];
  props.value.forEach((item: any, index: number) => {
    fileList.value.push({
      uid: index,
      name: item.split('/').pop(),
      status: 'success',
      url: item,
    });
  });
};
setInfo();

const emits = defineEmits(['update:value']);
watch(
  () => fileList.value,
  (newVal) => {
    let arr: any = [];
    newVal.forEach((item: any) => {
      if (item.status === 'success') {
        arr.push(item.url);
      }
    });
    emits('update:value', arr);
  },
  { immediate: true, deep: true },
);

const handleRemove = (file: any) => {
  fileList.value = fileList.value.filter((item: any) => item.uid !== file.uid);
};

// 自定义上传 ===========================================================================================
const customRequest = async (file: any) => {
  let fileVal = file.file || file;

  if (props.maxCount && fileList.value.length >= props.maxCount) {
    message.error(`最多只能上传 ${props.maxCount} 个文件`);
    return false;
  }

  let isValidType = getMediaType(fileVal.name);
  if (props.accept) {
    let acceptList = props.accept.split(',');
    if (!acceptList.includes(isValidType)) {
      message.error(`请上传 "${upNameList.value}" 格式的文件`);
      return false;
    }
  }

  if (fileVal.size > props.maxFileSize[isValidType] * 1024 * 1024) {
    message.error(
      `文件 ${fileVal.name} 大小超过 ${props.maxFileSize[isValidType]}M`,
    );
    return false;
  }

  let params = {
    uid: fileVal.uid,
    name: fileVal.name,
    status: 'uploading',
    url: '',
  };
  if (fileList.value.length < props.maxCount) {
    fileList.value.push(params);
    let res = await customOssUpload({
      file: fileVal,
      fileTypeTag: props.fileTypeTag,
      chunkSize: 1 * 1024 * 1024, //分片大小，默认1M
    });

    fileList.value.find((item: any) => item.uid === fileVal.uid).status =
      'success';
    fileList.value.find((item: any) => item.uid === fileVal.uid).url = res;
  }
};

// 图片预览 ===========================================================================================
const previewList: any = computed(() => {
  let arr = fileList.value.filter(
    (item: any) => item.url && getMediaType(item.url) === 'image',
  );
  return arr || [];
});
let previewIndex = ref(0);
const imgPreviewBtn = (file: any) => {
  previewIndex.value = previewList.value.findIndex(
    (item: any) => item.url === file.url,
  );
  visible.value = true;
};

// 视频，音频预览 ===========================================================================================
import { useVbenModal } from '@vben/common-ui';
const [Modal, modalApi] = useVbenModal({
  showConfirmButton: false,
  fullscreenButton: false,
});
let previewType = ref('');
let previewFile = ref('');

const previewBtn = (type: string, file: any) => {
  if (type === 'other') {
    window.open(file.url);
    return;
  }

  previewType.value = type;
  previewFile.value = file.url;
  modalApi.open();
};
</script>

<style scoped lang="scss"></style>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, nextTick } from 'vue';
import {
  Checkbox,
  Button,
  Row,
  Col,
  InputNumber,
  Select,
  message,
  Modal,
} from 'ant-design-vue';
import CusUpload from '#/components/CusUpload/index.vue';
import CusQrCode from '#/components/CusQrCode/index.vue';
const { Option } = Select;
import ticketBg from '#/assets/images/ticketBg.jpg';

const props = defineProps({
  value: {
    type: Object,
    default: () => ({}),
  },
});
const emit = defineEmits(['update:value', 'toggle-orientation']);

// 添加打印防抖状态变量
const printTestDebounce = ref(false);

// 配置项状态
let config = reactive({
  type: 'horizontal',
  ticketImg: [],
  // 页面设置
  page: {
    width: 150, // 纸张宽度(mm)
    height: 50, // 纸张高度(mm)
    marginTop: 0, // 横向时上边距为0
    marginBottom: 5,
    marginLeft: 2,
    marginRight: 2,
  },
  // 打印区设置
  blankArea: {
    width: 50, // 打印区宽度(mm)
    position: 'left', // 位置：left 或 right
  },
  // 二维码设置
  qrCode: {
    size: 60, // 二维码大小(px)
    position: 'top', // 二维码位置：left, right, top, bottom
  },
  // 门票信息
  info: {
    ticketCode: true,
    ticketNumber: true,
    personTime: true,
    entryDate: true,
    timeSlot: true,
    seat: true,
    purchaseTime: false,
  },
});

// 计算预览区样式 (1mm = 3.78px，约等于4px)
const previewStyle = computed(() => {
  const totalWidth = config.page.width;
  const totalHeight = config.page.height;

  return {
    width: `${totalWidth}mm`,
    height: `${totalHeight}mm`,
  };
});

// 计算背景图片的样式
const backgroundStyle = computed(() => {
  const imageWidth = config.page.width - config.blankArea.width;
  const imageHeight = config.page.height;

  return {
    width: `${imageWidth}mm`,
    height: `${imageHeight}mm`,
  };
});

// 计算门票打印区域的样式
const infoAreaStyle = computed(() => {
  const blankAreaWidth = config.blankArea.width;

  // 优化字体大小计算逻辑，使其更适合打印
  // 基于打印区域宽度计算字体大小，确保在不同尺寸下都有合适的可读性
  // 按用户要求，字体要比实际稍小点
  const baseFontSize = Math.max(12, Math.min(blankAreaWidth * 0.08, 16));

  return {
    width: `${blankAreaWidth}mm`,
    height: `${config.page.height}mm`,
    padding: `${config.page.marginTop}mm ${config.page.marginRight}mm ${config.page.marginBottom}mm ${config.page.marginLeft}mm`,
    fontSize: `${baseFontSize}px`,
  };
});

// 计算当前显示的图片源
const currentImageSrc = computed(() => {
  // 如果用户上传了图片，使用上传的图片
  if (
    config.ticketImg &&
    Array.isArray(config.ticketImg) &&
    config.ticketImg.length > 0
  ) {
    const firstImage = config.ticketImg[0] as any;
    if (firstImage && firstImage.url) {
      return firstImage.url;
    }
  }
  // 否则使用默认图片
  return ticketBg;
});

// 计算二维码是否显示在指定位置
const showQrCodeAt = computed(() => ({
  left: config.qrCode.position === 'left',
  right: config.qrCode.position === 'right',
  top: config.qrCode.position === 'top',
  bottom: config.qrCode.position === 'bottom',
}));

watch(
  () => config,
  (newConfig) => {
    const width = newConfig.page.width;
    const height = newConfig.page.height;

    emit('update:value', {
      margins: [
        newConfig.page.marginLeft,
        newConfig.page.marginTop,
        newConfig.page.marginRight,
        newConfig.page.marginBottom,
      ],
      width: width,
      height: height,
      templateConfig: newConfig,
    });
  },
  { deep: true, immediate: true },
);

watch(
  () => props.value,
  (newValue) => {
    if (newValue && newValue.templateConfig) {
      const templateConfig = newValue.templateConfig;

      // 安全地更新配置，只更新存在的字段
      if (templateConfig.ticketImg !== undefined) {
        config.ticketImg = templateConfig.ticketImg;
      }

      if (templateConfig.page) {
        // 确保 page 配置结构正确（横向需要 width 和 height）
        if (templateConfig.page.width !== undefined) {
          config.page.width = templateConfig.page.width;
        }
        if (templateConfig.page.height !== undefined) {
          config.page.height = templateConfig.page.height;
        }
        if (templateConfig.page.marginTop !== undefined) {
          config.page.marginTop = templateConfig.page.marginTop;
        }
        if (templateConfig.page.marginBottom !== undefined) {
          config.page.marginBottom = templateConfig.page.marginBottom;
        }
        if (templateConfig.page.marginLeft !== undefined) {
          config.page.marginLeft = templateConfig.page.marginLeft;
        }
        if (templateConfig.page.marginRight !== undefined) {
          config.page.marginRight = templateConfig.page.marginRight;
        }
      }

      if (templateConfig.blankArea) {
        // 确保 blankArea 配置结构正确（横向需要 width 和 left/right position）
        if (templateConfig.blankArea.width !== undefined) {
          config.blankArea.width = templateConfig.blankArea.width;
        }
        if (
          templateConfig.blankArea.position &&
          ['left', 'right'].includes(templateConfig.blankArea.position)
        ) {
          config.blankArea.position = templateConfig.blankArea.position;
        }
      }

      if (templateConfig.qrCode) {
        Object.assign(config.qrCode, templateConfig.qrCode);
      }

      if (templateConfig.info) {
        Object.assign(config.info, templateConfig.info);
      }
    }
  },
  {
    immediate: true,
    deep: true,
  },
);

// 切换横向竖向
const toggleOrientation = () => {
  emit('toggle-orientation');
};

// 测试打印
import { getLodop } from '#/utils/LodopFuncs';
import { printStyle } from './printStyle';
const printer = ref<any>(null);
const createPrint = () => {
  printer.value = getLodop();
  if (!printer.value) {
    message.error('未安装打印插件！');
    return;
  }
  // printer.value.PRINT_INIT('');
  // 设置打印纸张大小为实际配置的尺寸
  printer.value.SET_PRINT_PAGESIZE(
    2,
    config.page.height + 'mm',
    config.blankArea.width + 'mm',
    '门票',
  );
  // 设置打印区域为100%以避免内容被缩放
  printer.value.ADD_PRINT_HTM(
    0,
    0,
    '100%',
    '100%',
    printStyle() +
      '<body>' +
      document.getElementById('printContent')?.innerHTML +
      '</body>',
  );
  printer.value.SET_PRINT_MODE('FULL_WIDTH_FOR_OVERFLOW', true);

  printer.value.SET_PRINT_MODE('FULL_HEIGHT_FOR_OVERFLOW', true);
  printer.value.SET_LICENSES(
    '深圳市智络科技有限公司',
    'BBFF47D5AB0D522C0007D05CDE387E65',
    '',
    '',
  );
  console.log(
    printStyle() +
      '<body>' +
      document.getElementById('printContent')?.innerHTML +
      '</body>',
    '798',
  );
};
const printTest = () => {
  // 防抖，防止多次点击打印
  if (printTestDebounce.value) {
    return;
  }
  
  printTestDebounce.value = true;
  createPrint();
  return
  if (printer.value?.CVERSION) {
    // printer.value.PREVIEW(); // 预览
    printer.value.PRINT(); // 打印
    message.success('打印请求成功');
    
    // 3秒后重置防抖状态
    setTimeout(() => {
      printTestDebounce.value = false;
    }, 3000);
    return;
  } else {
    // Modal.warning({
    //   title: '提示',
    //   content:
    //     '打印错误，打印异常/缺纸。请检查打印纸状态或联系工作人员，确认是否安装Web打印服务CLodop。完成后请刷新本页面重试。',
    // });
    printTestDebounce.value = false;
  }
};
</script>

<template>
  <div class="flex flex-col gap-6">
    <!-- 预览区 -->
    <div class="w-full">
      <div class="mb-4 flex justify-between">
        <span class="text-lg font-semibold">模板设计</span>
        <div class="flex gap-2">
          <Button @click="printTest">打印测试</Button>
          <Button @click="toggleOrientation"> 切换为竖向 </Button>
          <CusUpload
            v-model="config.ticketImg"
            :maxCount="1"
            accept=".jpg,.png,.jpeg,.bmp,.gif,.webp"
            fileTypeTag="templateImg"
            listType="text"
            :showUploadList="false"
          >
            <Button type="primary">更换图片</Button>
          </CusUpload>
        </div>
      </div>
      <div
        class="flex min-h-[300px] flex-col items-center justify-center border border-gray-300 bg-gray-50 p-4"
      >
        <!-- 门票预览 -->
        <div class="flex bg-white shadow-lg" :style="previewStyle">
          <!-- 左侧打印区 -->
          <div
            id="printContent"
            class="border-r border-r-[3px] border-dashed border-black"
            v-if="config.blankArea.position === 'left'"
          >
            <div class="flex flex-col bg-white" :style="infoAreaStyle">
              <!-- 顶部二维码 -->
              <div
                v-if="config.info.ticketCode && showQrCodeAt.top"
                class="mb-2 flex flex-shrink-0 justify-center p-0"
              >
                <CusQrCode
                  text="V4CHEQ6P4E0"
                  :size="config.qrCode.size"
                ></CusQrCode>
              </div>

              <div
                class="flex flex-1"
                :class="{
                  'flex-row items-center':
                    showQrCodeAt.left || showQrCodeAt.right,
                  'flex-col': showQrCodeAt.top || showQrCodeAt.bottom,
                }"
              >
                <!-- 左侧二维码 -->
                <div
                  v-if="config.info.ticketCode && showQrCodeAt.left"
                  class="mr-2 flex flex-shrink-0 items-center justify-center p-0"
                >
                  <CusQrCode
                    text="V4CHEQ6P4E0"
                    :size="config.qrCode.size"
                  ></CusQrCode>
                </div>

                <!-- 门票信息 -->
                <div class="space-y-1">
                  <div v-if="config.info.ticketNumber" class="mb-1 flex">
                    <span class="lastJustify block w60 flex-shrink-0"
                      >票<span></span>号</span
                    >
                    <span class="flex-shrink-0">：</span>
                    <span class="flex-1 break-all">V4CHEQ6P4E0</span>
                  </div>
                  <div v-if="config.info.personTime" class="mb-1 flex">
                    <span class="lastJustify block w60 flex-shrink-0"
                      >人<span></span>次</span
                    >
                    <span class="flex-shrink-0">：</span>
                    <span class="flex-1">2</span>
                  </div>
                  <div v-if="config.info.entryDate" class="mb-1 flex">
                    <span class="lastJustify block w60 flex-shrink-0"
                      >入<span></span>园<span></span>日<span></span>期</span
                    >
                    <span class="flex-shrink-0">：</span>
                    <span class="flex-1">2025-07-24</span>
                  </div>
                  <div v-if="config.info.timeSlot" class="mb-1 flex">
                    <span class="lastJustify block w60 flex-shrink-0"
                      >场<span></span>次</span
                    >
                    <span class="flex-shrink-0">：</span>
                    <span class="flex-1">09:00-12:00</span>
                  </div>
                  <div v-if="config.info.seat" class="mb-1 flex">
                    <span class="lastJustify block w60 flex-shrink-0"
                      >座<span></span>位</span
                    >
                    <span class="flex-shrink-0">：</span>
                    <span class="flex-1">A区1排1座</span>
                  </div>
                  <div v-if="config.info.purchaseTime" class="mb-1 flex">
                    <span class="lastJustify block w60 flex-shrink-0"
                      >购<span></span>票<span></span>时<span></span>间</span
                    >
                    <span class="flex-shrink-0">：</span>
                    <span class="flex-1">2025-07-24 12:00</span>
                  </div>
                </div>

                <!-- 右侧二维码 -->
                <div
                  v-if="config.info.ticketCode && showQrCodeAt.right"
                  class="ml-2 flex flex-shrink-0 items-center justify-center p-0"
                >
                  <CusQrCode
                    text="V4CHEQ6P4E0"
                    :size="config.qrCode.size"
                  ></CusQrCode>
                </div>
              </div>

              <!-- 底部二维码 -->
              <div
                v-if="config.info.ticketCode && showQrCodeAt.bottom"
                class="mt-2 flex flex-shrink-0 justify-center p-0"
              >
                <CusQrCode
                  text="V4CHEQ6P4E0"
                  :size="config.qrCode.size"
                ></CusQrCode>
              </div>
            </div>
          </div>

          <!-- 背景图片 -->
          <img
            :src="currentImageSrc"
            alt="门票背景"
            class="object-cover"
            :style="backgroundStyle"
          />

          <!-- 右侧打印区 -->
          <div
            id="printContent"
            class="border-l border-l-[3px] border-dashed border-black"
            v-if="config.blankArea.position === 'right'"
          >
            <div class="flex flex-col bg-white" :style="infoAreaStyle">
              <!-- 顶部二维码 -->
              <div
                v-if="config.info.ticketCode && showQrCodeAt.top"
                class="mb-2 flex flex-shrink-0 justify-center p-0"
              >
                <CusQrCode
                  text="V4CHEQ6P4E0"
                  :size="config.qrCode.size"
                ></CusQrCode>
              </div>

              <div
                class="flex flex-1"
                :class="{
                  'flex-row': showQrCodeAt.left || showQrCodeAt.right,
                  'flex-col': showQrCodeAt.top || showQrCodeAt.bottom,
                }"
              >
                <!-- 左侧二维码 -->
                <div
                  v-if="config.info.ticketCode && showQrCodeAt.left"
                  class="mr-2 flex flex-shrink-0 items-center justify-center p-0"
                >
                  <CusQrCode
                    text="V4CHEQ6P4E0"
                    :size="config.qrCode.size"
                  ></CusQrCode>
                </div>

                <!-- 门票信息 -->
                <div class="space-y-1">
                  <div v-if="config.info.ticketNumber" class="mb-1 flex">
                    <span class="lastJustify block w60 flex-shrink-0"
                      >票<span></span>号</span
                    >
                    <span class="flex-shrink-0">：</span>
                    <span class="flex-1 break-all">V4CHEQ6P4E0</span>
                  </div>
                  <div v-if="config.info.personTime" class="mb-1 flex">
                    <span class="lastJustify block w60 flex-shrink-0"
                      >人<span></span>次</span
                    >
                    <span class="flex-shrink-0">：</span>
                    <span class="flex-1">2</span>
                  </div>
                  <div v-if="config.info.entryDate" class="mb-1 flex">
                    <span class="lastJustify block w60 flex-shrink-0"
                      >入<span></span>园<span></span>日<span></span>期</span
                    >
                    <span class="flex-shrink-0">：</span>
                    <span class="flex-1">2025-07-24</span>
                  </div>
                  <div v-if="config.info.timeSlot" class="mb-1 flex">
                    <span class="lastJustify block w60 flex-shrink-0"
                      >场<span></span>次</span
                    >
                    <span class="flex-shrink-0">：</span>
                    <span class="flex-1">09:00-12:00</span>
                  </div>
                  <div v-if="config.info.seat" class="mb-1 flex">
                    <span class="lastJustify block w60 flex-shrink-0"
                      >座<span></span>位</span
                    >
                    <span class="flex-shrink-0">：</span>
                    <span class="flex-1">A区1排1座</span>
                  </div>
                  <div v-if="config.info.purchaseTime" class="mb-1 flex">
                    <span class="lastJustify block w60 flex-shrink-0"
                      >购<span></span>票<span></span>时<span></span>间</span
                    >
                    <span class="flex-shrink-0">：</span>
                    <span class="flex-1">2025-07-24 12:00</span>
                  </div>
                </div>

                <!-- 右侧二维码 -->
                <div
                  v-if="config.info.ticketCode && showQrCodeAt.right"
                  class="ml-2 flex flex-shrink-0 items-center justify-center p-0"
                >
                  <CusQrCode
                    text="V4CHEQ6P4E0"
                    :size="config.qrCode.size"
                  ></CusQrCode>
                </div>
              </div>

              <!-- 底部二维码 -->
              <div
                v-if="config.info.ticketCode && showQrCodeAt.bottom"
                class="mt-2 flex flex-shrink-0 justify-center p-0"
              >
                <CusQrCode
                  text="V4CHEQ6P4E0"
                  :size="config.qrCode.size"
                ></CusQrCode>
              </div>
            </div>
          </div>
        </div>
        <div class="text-center text-xs text-gray-500">*以实际打印效果为准</div>
      </div>
    </div>

    <!-- 配置区 -->
    <div class="w-full">
      <div class="flex flex-col gap-6">
        <!-- 页面设置 -->
        <div>
          <h4 class="mb-3 font-semibold">页面设置</h4>
          <div class="space-y-3">
            <Row :gutter="8">
              <Col :span="12">
                <div class="flex items-center gap-2">
                  <span class="w-18 text-sm">纸张宽度：</span>
                  <InputNumber
                    v-model:value="config.page.width"
                    :min="50"
                    size="small"
                  />
                  <span class="text-sm text-gray-500">mm</span>
                </div>
              </Col>
              <Col :span="12">
                <div class="flex items-center gap-2">
                  <span class="w-18 text-sm">纸张高度：</span>
                  <InputNumber
                    v-model:value="config.page.height"
                    :min="30"
                    size="small"
                  />
                  <span class="text-sm text-gray-500">mm</span>
                </div>
              </Col>
            </Row>
          </div>
        </div>

        <!-- 打印区设置 -->
        <div>
          <h4 class="mb-3 font-semibold">打印区设置</h4>
          <div class="space-y-3">
            <Row :gutter="8">
              <Col :span="12">
                <div class="flex items-center gap-2">
                  <span class="w-18 text-sm">打印区宽度：</span>
                  <InputNumber
                    v-model:value="config.blankArea.width"
                    :min="30"
                    size="small"
                  />
                  <span class="text-sm text-gray-500">mm</span>
                </div>
              </Col>
              <Col :span="12">
                <div class="flex items-center gap-2">
                  <span class="w-18 text-sm">打印区位置：</span>
                  <Select
                    v-model:value="config.blankArea.position"
                    size="small"
                    style="width: 100px"
                  >
                    <Option value="left">左侧</Option>
                    <Option value="right">右侧</Option>
                  </Select>
                </div>
              </Col>
            </Row>
            <Row :gutter="8" class="mt-2">
              <Col :span="12">
                <div class="flex items-center gap-2">
                  <span class="w-15 text-sm">上边距：</span>
                  <InputNumber
                    v-model:value="config.page.marginTop"
                    :min="0"
                    size="small"
                  />
                  <span class="text-sm text-gray-500">mm</span>
                </div>
              </Col>
              <Col :span="12">
                <div class="flex items-center gap-2">
                  <span class="w-15 text-sm">下边距：</span>
                  <InputNumber
                    v-model:value="config.page.marginBottom"
                    :min="0"
                    size="small"
                  />
                  <span class="text-sm text-gray-500">mm</span>
                </div>
              </Col>
            </Row>
            <Row :gutter="8" class="mt-2">
              <Col :span="12">
                <div class="flex items-center gap-2">
                  <span class="w-15 text-sm">左边距：</span>
                  <InputNumber
                    v-model:value="config.page.marginLeft"
                    :min="0"
                    size="small"
                  />
                  <span class="text-sm text-gray-500">mm</span>
                </div>
              </Col>
              <Col :span="12">
                <div class="flex items-center gap-2">
                  <span class="w-15 text-sm">右边距：</span>
                  <InputNumber
                    v-model:value="config.page.marginRight"
                    :min="0"
                    size="small"
                  />
                  <span class="text-sm text-gray-500">mm</span>
                </div>
              </Col>
            </Row>
          </div>
        </div>

        <!-- 二维码设置 -->
        <div>
          <h4 class="mb-3 font-semibold">二维码设置</h4>
          <div class="space-y-3">
            <Row :gutter="8">
              <Col :span="12">
                <div class="flex items-center gap-2">
                  <span class="w-18 text-sm">二维码大小：</span>
                  <InputNumber
                    v-model:value="config.qrCode.size"
                    :min="40"
                    :max="200"
                    size="small"
                  />
                  <span class="text-sm text-gray-500"></span>
                </div>
              </Col>
              <Col :span="12">
                <div class="flex items-center gap-2">
                  <span class="w-18 text-sm">二维码位置：</span>
                  <Select
                    v-model:value="config.qrCode.position"
                    size="small"
                    style="width: 100px"
                  >
                    <Option value="left">左侧</Option>
                    <Option value="right">右侧</Option>
                    <Option value="top">顶部</Option>
                    <Option value="bottom">底部</Option>
                  </Select>
                </div>
              </Col>
            </Row>
          </div>
        </div>

        <!-- 门票信息设置 -->
        <div>
          <h4 class="mb-3 font-semibold">门票信息设置</h4>
          <div class="space-y-3">
            <Row :gutter="8">
              <Col :span="6">
                <Checkbox v-model:checked="config.info.ticketCode"
                  >二维码</Checkbox
                >
              </Col>
              <Col :span="6">
                <Checkbox v-model:checked="config.info.ticketNumber"
                  >票号</Checkbox
                >
              </Col>
              <Col :span="6">
                <Checkbox v-model:checked="config.info.personTime"
                  >人次</Checkbox
                >
              </Col>
              <Col :span="6">
                <Checkbox v-model:checked="config.info.entryDate"
                  >入园日期</Checkbox
                >
              </Col>
              <Col :span="6">
                <Checkbox v-model:checked="config.info.timeSlot">场次</Checkbox>
              </Col>
              <Col :span="6">
                <Checkbox v-model:checked="config.info.seat">座位</Checkbox>
              </Col>
              <Col :span="6">
                <Checkbox v-model:checked="config.info.purchaseTime"
                  >购票时间</Checkbox
                >
              </Col>
            </Row>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.w60 {
  width: 60px;
}
.lastJustify {
  text-align: justify;
  text-align-last: justify;
}

@media print {
  @page {
    size: v-bind('config.blankArea.width + "mm"')
      v-bind('config.page.height + "mm"') !important;
    margin: 0;
  }
  body {
    font-size: 8px !important;
    margin: 0;
    padding: 0;
    transform: scale(1) !important;
    transform-origin: top left;
  }

  #printContent {
    display: flex !important;
    font-size: inherit !important;
    width: 100% !important;
    height: 100% !important;
    position: relative !important;
    page-break-after: avoid !important;
    page-break-inside: avoid !important;
    break-inside: avoid !important;
    overflow: hidden !important;
  }

  #printContent :deep(.text-\[14px\]) {
    font-size: inherit !important;
  }

  #printContent :deep(.space-y-1) > div {
    font-size: inherit !important;
  }

  #printContent :deep(.space-y-1) > div span {
    font-size: inherit !important;
  }

  /* 确保打印时信息区域的尺寸与预览时一致 */
  #printContent :deep(.flex-col) {
    width: 100% !important;
    height: 100% !important;
    page-break-after: avoid !important;
    page-break-inside: avoid !important;
    break-inside: avoid !important;
  }

  /* 防止分页导致打印到第二张纸 */
  #printContent :deep(.flex) {
    page-break-inside: avoid !important;
    break-inside: avoid !important;
  }

  #printContent :deep(.space-y-1) {
    page-break-inside: avoid !important;
    break-inside: avoid !important;
  }
}
</style>

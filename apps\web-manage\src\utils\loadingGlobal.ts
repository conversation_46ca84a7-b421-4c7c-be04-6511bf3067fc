// index.js  创建自定义方法
/**
 * @description 创建或者关闭全屏加载状态效果
 * @param {boolean} visible true 打开全屏加载状态，false 关闭全屏加载状态
 */

export const fullScreenLoading = (visible: any) => {
  if (visible) {
    const fragment = new DocumentFragment();
    const loadDom = document.createElement('div');
    loadDom.setAttribute('class', 'myFullSpin');
    const loadChild = document.createElement('div');
    loadChild.setAttribute('class', 'ant-spin-container ant-spin-blur');
    const spinSpan = document.createElement('span');
    spinSpan.setAttribute('class', 'ant-spin ant-spin-dot ant-spin-dot-spin');
    for (let i = 0; i < 4; i++) {
      const liDom = document.createElement('i');
      liDom.setAttribute('class', 'ant-spin-dot-item');
      spinSpan.append(liDom);
    }
    loadDom.append(spinSpan);
    loadDom.append(loadChild);
    fragment.append(loadDom);
    document.body.append(fragment);
  } else {
    const loading = document.querySelector('.myFullSpin');
    if (loading) {
      loading.remove();
    }
  }
};

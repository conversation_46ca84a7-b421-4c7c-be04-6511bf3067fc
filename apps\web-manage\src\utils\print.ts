import { ref } from 'vue';
import { getLodop } from '#/utils/LodopFuncs';
import { message } from 'ant-design-vue';
import { printStyle } from '#/views/manageModule/system/printConfig/modules/printTemplate/generalTicket/printStyle';
import dayjs from 'dayjs';
import QRCode from 'qrcode';

const codeUrl = ref('');
const printer = ref<any>(null);

// 普通门票小票
const generalTicket = (data: any, config: any) => {
  // 根据内容js生成二维码base64图片
  createQrCode(data.detail.verificationCode, config.qrCode.size);
  let html =
    printStyle() +
    `<body><div class="flex ${config.type == 'horizontal' ? 'flex-col' : 'flex-row w-full items-center'} bg-white" style="width: ${config.type == 'horizontal' ? config.blankArea.width : config.page.width}mm; height: ${config.type == 'horizontal' ? config.page.height : config.blankArea.height}mm; padding: ${config.page.marginTop}mm ${config.page.marginRight}mm ${config.page.marginBottom}mm ${config.page.marginLeft}mm; font-size: 12px;">
    <!-- 顶部二维码 -->${
      config.info.ticketCode && config.qrCode.position === 'top'
        ? `<div class="mb-2 flex flex-shrink-0 justify-center p-0"><img src="${codeUrl.value}" alt="QR Code" /></div>`
        : ''
    }<div class="flex flex-1 ${config.type == 'horizontal' ? 'flex-col' : 'flex-row w-full items-center'}"><!-- 左侧二维码 -->${
      config.info.ticketCode && config.qrCode.position === 'left'
        ? `<div class="mr-2 flex flex-shrink-0 items-center justify-center p-0"><img src="${codeUrl.value}" alt="QR Code" /></div>`
        : ''
    }
    <!-- 门票信息 --><div class="${config.type == 'vertical' ? 'flex-1' : ''} space-y-1">
    ${
      config.info.ticketCode
        ? `<div class="mb-1 flex"><span class="lastJustify block w60 flex-shrink-0">票<span></span>号</span><span class="flex-shrink-0">：</span><span class="flex-1 break-all">${data.detail.verificationCode}</span></div>`
        : ''
    }
    ${config.info.ticketNumber ? `<div class="mb-1 flex"><span class="lastJustify block w60 flex-shrink-0">人<span></span>次</span><span class="flex-shrink-0">：</span><span class="flex-1">${data.item.ticketNum}</span></div>` : ''}
    ${config.info.entryDate ? `<div class="mb-1 flex"><span class="lastJustify block w60 flex-shrink-0">入<span></span>园<span></span>日<span></span>期</span><span class="flex-shrink-0">：</span><span class="flex-1">${data.item.playDate}</span></div>` : ''}
    ${config.info.timeSlot && data.item.periodId ? `<div class="mb-1 flex"><span class="lastJustify block w60 flex-shrink-0">场<span></span>次</span><span class="flex-shrink-0">：</span><span class="flex-1">${data.item.periodBeginTime + '~' + data.item.periodEndTime}</span></div>` : ''}
    <div class="mb-1 flex"><span class="lastJustify block w60 flex-shrink-0">座<span></span>位</span><span class="flex-shrink-0">：</span><span class="flex-1">--</span></div>
    ${config.info.purchaseTime ? `<div class="mb-1 flex"><span class="lastJustify block w60 flex-shrink-0">购<span></span>票<span></span>时<span></span>间</span><span class="flex-shrink-0">：</span><span class="flex-1">${data.item.createdAt}</span></div>` : ''}</div>
    <!-- 右侧二维码 -->${
      config.info.ticketCode && config.qrCode.position === 'right'
        ? `<div class="ml-2 flex flex-shrink-0 items-center justify-center p-0"><img src="${codeUrl.value}" alt="QR Code" /></div>`
        : ''
    }</div>
    <!-- 底部二维码 --> ${
      config.info.ticketCode && config.qrCode.position === 'bottom'
        ? `<div class="mt-2 flex flex-shrink-0 justify-center p-0"><img src="${codeUrl.value}" alt="QR Code" /></div>`
        : ''
    }
    </div></div></body>`;
  return html;
};

// 门票小票
const ticketTemplate = (data: any, config: any) => {
  console.log(data, 'data');
  // 根据内容js生成二维码base64图片
  createQrCode(data.detail.verificationCode, config.qrCode.size);
  let html =
    printStyle() +
    `<body><!-- 小票预览 --><div class="text-13 bg-white shadow-lg" style="width: ${config.page.width}mm; padding: ${config.page.marginTop}mm ${config.page.marginRight}mm ${config.page.marginBottom}mm ${config.page.marginLeft}mm; font-family: monospace;"><!-- 抬头 --><div class="mb-1 text-center">
    ${config.header.scenicName ? `<div class="text-14 font-bold leading-7"> ${data.scenicName} </div>` : ''}</div><!-- 门票信息 --><div class="leading-normal">
     ${config.info.ticketCode ? `<div class="mb-2 flex flex-shrink-0 items-center justify-center p-0"><img src="${codeUrl.value}" alt="QR Code" /></div>` : ''}
     ${config.info.ticketNumber ? `<div class="mb-1.5 flex"><span class="lastJustify w56">票<span></span>号</span><span >：</span><span class="flex-1">${data.detail.verificationCode}</span></div>` : ''}
     ${config.info.ticketName ? `<div class="mb-1.5 flex"><span class="lastJustify w56">票<span ></span>名</span><span >：</span><span class="flex-1">${data.detail.ticketName}</span></div>` : ''}
     ${config.info.personTime ? `<div class="mb-1.5 flex"><span class="lastJustify w56">人<span ></span>次</span><span >：</span><span class="flex-1">${data.item.ticketNum}</span></div>` : ''}
     ${config.info.price ? `<div class="mb-1.5 flex"><span class="lastJustify w56">单<span ></span>价</span><span >：</span><span class="flex-1">${data.item.unitPrice}</span></div>` : ''}
     ${config.info.sum ? `<div class="mb-1.5 flex"><span class="lastJustify w56">金<span ></span>额</span><span >：</span><span class="flex-1">${data.amount}</span></div>` : ''}
     ${
       config.info.validTime
         ? `<div  class="mb-1.5 flex"><span class="lastJustify w56">有<span ></span>效<span></span>期</span><span >：</span><span class="flex-1">${
             data.item.validType == 1
               ? data.item.validBeginDate + '当天有效'
               : data.item.validBeginDate
                 ? data.item.validBeginDate + '~' + data.item.validEndDate
                 : ''
           }</span></div>`
         : ''
     }
     </div><!-- 分割线 -->
     ${
       (config.sell.ticketSeller && data.sellerInfo.seller) ||
       (config.sell.ticketPoint && data.sellerInfo.point) ||
       config.sell.printTime
         ? `<div class="divider dashed"></div>`
         : ''
     }
     <!-- 售票信息 -->
     <div class="leading-normal">
     ${config.sell.ticketSeller || config.sell.ticketPoint ? '<div class="mb-1.5 text-center font-medium"> 售票信息 </div>' : ''}
     ${config.sell.ticketSeller && data.sellerInfo.seller ? `<div class="mb-1.5 text-center"> 售票员：${data.sellerInfo.seller} </div>` : ''}
     ${config.sell.ticketPoint && data.sellerInfo.point ? `<div class="mb-1.5 text-center"> 售票点：${data.sellerInfo.point} </div>` : ''}
     </div>
     <!-- 分割线 -->
     ${config.sell.printTime ? `<div class="divider dashed"></div>` : ''}
     <!-- 打印时间 --><!-- 分割线 --><div class="leading-normal">
     ${config.sell.printTime ? `<div class="mb-1.5 flex"><span class="lastJustify w56">打<span ></span>印<span></span>时<span ></span>间</span><span>：</span><span class="flex-1">${dayjs().format('YYYY-MM-DD HH:mm:ss')}</span></div>` : ''}</div>
     <!-- 分割线 -->
     ${config.footer.endnote ? `<div class="divider dashed"></div>` : ''}
     <!-- 尾注 -->
     ${config.footer.endnote ? `<div class="text-center leading-tight"><div  class="mb-1">${config.footer.endnoteText}</div></div>` : ''}</div></body>`;

  return html;
};

// 收银小票
const cashierTemplate = (data: any, config: any) => {
  // 根据内容js生成二维码base64图片
  createQrCode(data.detail.verificationCode, config.qrCode.size);
  let html = printStyle() + ``;
  return html;
};
// 租赁小票
const leaseTemplate = (data: any, config: any) => {
  // 根据内容js生成二维码base64图片
  createQrCode(data.detail.verificationCode, config.qrCode.size);
  let html = printStyle() + ``;
  return html;
};
// 充值小票
const rechargeTemplate = (data: any, config: any) => {
  // 根据内容js生成二维码base64图片
  createQrCode(data.detail.verificationCode, config.qrCode.size);
  let html = printStyle() + ``;
  return html;
};
// 生成二维码
const createQrCode = (content: any, size: any) => {
  QRCode.toDataURL(
    content,
    {
      errorCorrectionLevel: 'H',
      margin: 0,
      width: size || 100,
    },
    (err, url) => {
      if (err) console.error(err);
      codeUrl.value = url;
    },
  );
};
export function print(data: any, templateConfig: any, templateType: any) {
  let config = JSON.parse(templateConfig);
  printer.value = getLodop();

  if (!printer.value) {
    message.error('未安装打印插件！');
    return;
  }
  // printer.value.PRINT_INIT(打印任务名称);
  if (templateType == 1) {
    // printer.value.PRINT_INIT('');
    // 设置打印纸张大小为实际配置的尺寸
    if (config.type == 'horizontal') {
      printer.value.SET_PRINT_PAGESIZE(
        2,
        config.page.height + 'mm',
        config.blankArea.width + 'mm',
        '门票',
      );
    } else {
      printer.value.SET_PRINT_PAGESIZE(
        1,
        config.page.width + 'mm',
        config.blankArea.height + 'mm',
        '门票',
      );
    }
    // 设置打印区域为100%以避免内容被缩放
    printer.value.ADD_PRINT_HTM(
      0,
      0,
      '100%',
      '100%',
      generalTicket(data, config),
    );
    printer.value.SET_PRINT_MODE('FULL_WIDTH_FOR_OVERFLOW', true);

    printer.value.SET_PRINT_MODE('FULL_HEIGHT_FOR_OVERFLOW', true);
  } else {
    printer.value.SET_PRINT_PAGESIZE(3, config.page.width + 'mm', '', '');
    printer.value.SET_PRINT_MODE('PRINT_PAGE_PERCENT', 'Height:100%');
    printer.value.SET_PRINT_MODE('PRINT_PAGE_PERCENT', 'Auto-Width');
    // 设置打印区域为100%以避免内容被缩放
    printer.value.ADD_PRINT_HTM(
      0,
      0,
      '100%',
      '90%',
      ticketTemplate(data, config),
    );
  }
  printer.value.SET_LICENSES(
    '深圳市智络科技有限公司',
    'BBFF47D5AB0D522C0007D05CDE387E65',
    '',
    '',
  );
  printer.value.PRINT(); // 打印
  //   printer.value.PREVIEW(); // 预览
  message.success('打印请求成功');
}

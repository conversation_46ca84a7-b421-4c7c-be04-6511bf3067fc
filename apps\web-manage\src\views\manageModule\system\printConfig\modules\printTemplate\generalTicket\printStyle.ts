export function printStyle() {
  return `
    <style>
    .flex{
      display: flex;
    }
    .flex-1{
      flex: 1;
    }
    .flex-row{
      flex-direction: row;
    }
    .flex-col{
      flex-direction: column;
    }
    .flex-shrink-0{
      flex-shrink: 0;
    }
    .justify-between{
      justify-content: space-between;
    }
    .justify-center{
      justify-content: center;
    }
    .items-center{
      align-items: center;
    }
    .bg-white{
      background-color: white;
    }
    .shadow-lg{
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }
    .mt-2{
      margin-top: 0.5rem;
    }
    .mb-2{
      margin-bottom: 0.5rem;
    }
    .mb-1{
      margin-bottom: 0.25rem;
    }
    .ml-2{
      margin-left: 0.5rem;
    }
    .mr-2{
      margin-right: 0.5rem;
    }
    .p-0{
      padding: 0;
    }
    .break-all{
      word-break: break-all;
    }
    .block{
      display: block;
    }
    .w-full{
      width: 100%;
    }
    .w-60 {
      width: 60%;
    }
    .w-15 {
      width: 15%;
    }
    .w-30{
      width: 30%;
    }
    .w-25 {
      width: 25%;
    }
    .w-70{
      width: 70%;
    }
    .leading-normal{
      line-height: 1.5;
    }
    .leading-tight{
      line-height: 1.25;
    }
    .leading-7 {
      line-height: 1.75;
    }
    .w60{
      width: 55px;
      display:flex;
    }
    .lastJustify span{
      flex: 1;
    }
    .text-center{
      text-align: center;
    }
    .w56{
      width: 56px;
      display:flex;
    }
    .text-12 {
      font-size: 12px;
    }
    .text-13 {
      font-size: 13px;
    }
    .text-14 {
      font-size: 14px;
    }
    /* 添加分割线样式 */
    .divider {
      height: 1px;
      border-bottom: 1px dashed  rgba(0, 0, 0, 1);
      margin: 4px 0;
      position: relative;
    }
    </style>
`;
}
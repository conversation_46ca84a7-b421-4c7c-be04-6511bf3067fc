<script setup lang="ts">
import { toRefs, ref, onMounted } from 'vue';
import { Page } from '@vben/common-ui';
import { getRefundInfo } from '#/api/manageModule';
import {
  PageHeader,
  Descriptions,
  DescriptionsItem,
  Card,
  Table,
} from 'ant-design-vue';
import type { TableColumnType } from 'ant-design-vue';
import { useAccessStore } from '@vben/stores';
import { useRoute } from 'vue-router';
const { accessAllEnums } = toRefs(useAccessStore());
import RefundLog from './modules/refundLog.vue';

const route = useRoute();
const refundId = ref<any>(route.query.id);
const refundInfo = ref<any>({});
const getRefundInfoData = async () => {
  const res = await getRefundInfo(refundId.value);
  refundInfo.value = res;
};

onMounted(() => {
  getRefundInfoData();
});

const columns1: TableColumnType[] = [
  {
    title: '核销码',
    dataIndex: 'verificationCode',
    width: 180,
    customRender: ({ record }: any) => {
      return record.ticketOrderDetail.verificationCode || '--';
    },
  },
  {
    title: '门票名称',
    dataIndex: 'ticketName',
    width: 120,
    align: 'center',
    customRender: ({ record }: any) => {
      return record.ticketOrderDetail.ticketName || '--';
    },
  },
  {
    title: '退款金额',
    dataIndex: 'refundPrice',
    width: 120,
    align: 'center',
    customRender: ({ record }: any) => {
      return record.orderPrice || '--';
    },
  },
  {
    title: '退款手续费',
    dataIndex: 'refundFeePrice',
    width: 120,
    align: 'center',
    customRender: ({ record }: any) => {
      return record.orderPrice || '--';
    },
  },
  {
    title: '出行人',
    dataIndex: 'touristName',
    width: 120,
    align: 'center',
    customRender: ({ record }: any) => {
      return record.ticketOrderDetail.touristName || '--';
    },
  },
  {
    title: '手机号',
    dataIndex: 'touristPhone',
    width: 120,
    align: 'center',
    customRender: ({ record }: any) => {
      return record.ticketOrderDetail.touristPhone || '--';
    },
  },
  {
    title: '身份证',
    dataIndex: 'touristIdcard',
    width: 180,
    align: 'center',
    customRender: ({ record }: any) => {
      return record.ticketOrderDetail.touristIdcard || '--';
    },
  },
];
const filterText = (arr: any[], val: any) => {
  return arr.find((item: any) => item.value === val)?.label;
};
</script>
<template>
  <Page>
    <div class="bg-card">
      <PageHeader title="退款详情" class="p-3" @back="() => $router.back()">
        <div class="px-6">
          <Descriptions>
            <DescriptionsItem label="订单号">{{
              refundInfo.orderInfo?.orderNo
            }}</DescriptionsItem>
            <DescriptionsItem label="退单号">{{
              refundInfo.refundNo
            }}</DescriptionsItem>
            <DescriptionsItem label="联系人">
              {{ refundInfo.orderInfo?.userName }}
              <span class="ml-2">{{ refundInfo.orderInfo?.userPhone }}</span>
            </DescriptionsItem>
            <DescriptionsItem label="退款数量">{{
              refundInfo.refundNum
            }}</DescriptionsItem>
            <DescriptionsItem label="申请时间">{{
              refundInfo.applyTime
            }}</DescriptionsItem>
            <DescriptionsItem label="退款原因">
              {{ refundInfo.refundReason }}
            </DescriptionsItem>
          </Descriptions>
        </div>
      </PageHeader>
      <div class="px-3 pb-3">
        <template
          v-for="(item, index) in refundInfo.refundItemList"
          :key="item.id"
        >
          <Card class="mb-5">
            <h3 class="mb-2 text-[16px] font-[600]">退款信息</h3>
            <Descriptions>
              <DescriptionsItem label="门票名称">{{
                item.ticketName
              }}</DescriptionsItem>
              <DescriptionsItem label="退票数量">{{
                item.refundNum
              }}</DescriptionsItem>
              <DescriptionsItem label="退款手续费">{{
                refundInfo.refundFeePrice
              }}</DescriptionsItem>
              <DescriptionsItem label="退款金额">{{
                refundInfo.refundPrice
              }}</DescriptionsItem>
              <DescriptionsItem label="退款状态">{{
                filterText(
                  accessAllEnums?.orderRefundStatus.list,
                  refundInfo.refundStatus,
                )
              }}</DescriptionsItem>
            </Descriptions>
            <div class="mb-2 text-[16px] font-[600]">退款子订单信息</div>
            <Table
              :columns="columns1"
              :data-source="item.refundDetailList"
              :pagination="false"
            ></Table>
          </Card>
        </template>
        <RefundLog :refundId="refundId"></RefundLog>
      </div>
    </div>
  </Page>
</template>

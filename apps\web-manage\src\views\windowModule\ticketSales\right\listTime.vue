<script lang="ts" setup>
import { ref, watch } from 'vue';
import { CheckOutlined } from '@ant-design/icons-vue';
import { Tooltip } from 'ant-design-vue';

let props = defineProps(['value', 'list', 'searchInfo']);

// =====================================================================================
const selectVal: any = ref(null);
let emits = defineEmits(['update:value']);
watch(
  () => selectVal.value,
  (newValue) => {
    emits('update:value', newValue);
  },
  { immediate: true, deep: true },
);
// =====================================================================================
const isCurrentTimePeriod = (e: any) => {
  // 获取当前时间（格式为 "HH:mm"）
  const now: any = new Date();
  const currentDate = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
  const pad = (n: number) => n.toString().padStart(2, '0');
  const current = `${pad(now.getHours())}:${pad(now.getMinutes())}`;

  // 比较时间字符串e
  let str = 'cursor-pointer';
  if (
    currentDate === props.searchInfo.playDate &&
    e.beginTime <= current &&
    current <= e.endTime
  ) {
    str = 'tip text-color-warning border-color-warning cursor-pointer';
  }

  if (selectVal.value === e.id) {
    str =
      'tip bg-color-primary text-color-white border-color-primary cursor-pointer';
  }

  if (!e.stockInfo.availableStock) {
    str = 'bg-color-quickBox cursor-no-drop';
  }
  return str;
};
</script>

<template>
  <div class="overflow-hidden">
    <div class="overflow-hidden" style="margin: -0.5rem 0 0 -0.5rem">
      <div
        v-for="item in list"
        class="relative float-left ml-2 mt-2 overflow-hidden rounded-lg border px-2 py-1"
        :class="[isCurrentTimePeriod(item)]"
        @click="if (item.stockInfo.availableStock) selectVal = item.id;"
      >
        <Tooltip
          :title="
            isCurrentTimePeriod(item).includes('tip')
              ? `请注意，此时段 ${item.endTime} 后到期`
              : ''
          "
          color="red"
        >
          <div>{{ item.beginTime }}~{{ item.endTime }}</div>
          <div class="flex items-center justify-center gap-1 text-xs">
            <div>库存:</div>
            <div v-if="item.stockInfo.availableStock === -1">不限</div>
            <div v-else>
              {{
                item.stockInfo.availableStock > 1000
                  ? '999+'
                  : item.stockInfo.availableStock
              }}
            </div>
          </div>
        </Tooltip>

        <!-- <div v-if="selectVal === item.id">
          <div
            class="bg-color-primary flex-center absolute bottom-0 right-0 translate-x-4 translate-y-4 rotate-45 p-2"
          >
            <CheckOutlined class="opacity-0" />
          </div>
          <CheckOutlined
            class="text-color-white absolute bottom-0 right-0 scale-75 text-[12px]"
          />
        </div> -->
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>

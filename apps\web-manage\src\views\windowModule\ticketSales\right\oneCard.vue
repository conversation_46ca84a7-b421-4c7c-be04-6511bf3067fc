<script lang="ts" setup>
import { ref } from 'vue';
import { useVbenModal } from '@vben/common-ui';

import type { VxeGridProps } from '#/adapter/vxe-table';

import { Form, FormItem, Input, Button } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { cardList } from '#/api/windowModule/prepaidCard';

// ========================================================================================
let openVal: any = ref({});
const open = async (e: any) => {
  openVal.value = e;
  if (e.userPhone) formState.value.phone = e.userPhone;
  modalApi.open();
};
defineExpose({ open });

// =========================================================================================
let emits = defineEmits(['cancel', 'confirm']);
const [Modal, modalApi] = useVbenModal({
  destroyOnClose: true,
  fullscreenButton: false,
  closable: false,
  closeOnClickModal: false,
  onCancel() {
    emits('cancel');
    modalApi.close();
  },
  onConfirm() {
    let getData = GridApi.grid.getRadioRecord();
    emits('confirm', getData);
    modalApi.close();
  },
});

// =========================================================================================
let formState = ref<any>({});

const gridOptions: VxeGridProps<any> = {
  rowConfig: {
    isCurrent: true,
  },
  radioConfig: {
    labelField: 'cardNo',
  },
  columnConfig: {
    resizable: false,
  },
  align: 'left',
  columns: [
    { type: 'radio', title: '卡号' },
    { field: 'name', title: '姓名' },
    { field: 'phone', title: '手机号码' },
    { field: 'balance', title: '余额' },
  ],
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }) => {
        let resData = await cardList({
          page: page.currentPage,
          pageSize: page.pageSize,
          ...formState.value,
        });
        return {
          items: resData.list,
          total: resData.total,
        };
      },
    },
  },
};

const [Grid, GridApi] = useVbenVxeGrid({ gridOptions });
</script>

<template>
  <Modal title="一卡通搜索" class="w-[800px]">
    <div class="mb-1 px-2">
      <Form :model="formState">
        <div class="grid grid-cols-4 gap-3">
          <FormItem class="m-0">
            <Input v-model:value="formState.cardNo" placeholder="请输入卡号" />
          </FormItem>
          <FormItem class="m-0">
            <Input v-model:value="formState.name" placeholder="请输入姓名" />
          </FormItem>
          <FormItem class="m-0">
            <Input v-model:value="formState.phone" placeholder="请输入手机号" />
          </FormItem>
          <FormItem class="m-0">
            <Button @click="((formState = {}), GridApi.query())">重置</Button>
            <Button type="primary" class="ml-3" @click="GridApi.query()">
              搜索
            </Button>
          </FormItem>
        </div>
      </Form>
    </div>
    <div class="h-[500px]">
      <Grid />
    </div>
  </Modal>
</template>

<style lang="scss" scoped></style>

<script lang="ts" setup>
import { Tooltip } from 'ant-design-vue';
import { IconifyIcon } from '@vben/icons';
import { useRouter, useRoute } from 'vue-router';
const router = useRouter();
const route = useRoute();

import { useAccessStore } from '@vben/stores';
const accessStore = useAccessStore();

let listArr: any = accessStore.accessRoutes.filter((i: any) =>
  i.path.includes('/window'),
);

// ========================================================================================
import { useVbenModal } from '@vben/common-ui';
const [Modal, modalApi] = useVbenModal({
  footer: false,
  header: false,
  fullscreenButton: false,
  closable: false,
  destroyOnClose: true,
});
</script>

<template>
  <div v-if="route.path != '/window/home'" class="py-4">
    <div
      class="w-full rounded-lg py-3"
      style="
        background: rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(20px);
        box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 10px 5px;
      "
    >
      <div class="flex-center flex gap-6">
        <div
          v-for="item in listArr"
          :key="item.path"
          class="cursor-pointer"
          @click="router.push(item.path)"
        >
          <Tooltip :title="item.meta.title">
            <div class="bg-color-white rounded-lg p-3">
              <IconifyIcon
                :icon="item.meta?.icon || 'mdi:menu'"
                class="size-5"
              />
            </div>
          </Tooltip>
        </div>

        <div
          v-if="listArr.length > 15"
          class="cursor-pointer"
          @click="() => modalApi.open()"
        >
          <Tooltip title="更多">
            <div class="bg-color-white rounded-lg p-3">
              <IconifyIcon icon="mdi:menu" class="size-6" />
            </div>
          </Tooltip>
        </div>
      </div>
    </div>

    <!-- =========================================================== -->
    <Modal class="w-[600px]" title="基础示例">
      <div class="flex-center flex gap-6 p-5 text-sm">
        <div
          v-for="item in listArr"
          :key="item.path"
          class="cursor-pointer"
          @click="router.push(item.path)"
        >
          <div class="bg-color-quickBox rounded-lg p-3">
            <IconifyIcon :icon="item.meta?.icon || 'mdi:menu'" class="size-6" />
          </div>
          <div class="mt-1 text-center">{{ item.meta.title }}</div>
        </div>
      </div>
    </Modal>
  </div>
</template>

import type { VbenFormSchema } from '#/adapter/form';

import { z } from '#/adapter/form';

export function useInfoFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'CusUpload',
      fieldName: 'avatar',
      label: '头像',
      componentProps: {
        maxCount: 1,
        accept: '.jpg,.png,.jpeg,.bmp,.gif,.webp',
        fileTypeTag: 'avatar',
        multiple: false,
        showUploadList: true,
        listType: 'picture-card',
      },
      rules: z.array(z.object({ url: z.string().url() })).min(1, '请上传头像'),
    },
    {
      component: 'Input',
      fieldName: 'name',
      label: '姓名',
      rules: 'required',
      componentProps: {
        placeholder: '请输入姓名',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'phone',
      label: '手机号',
      rules: 'required',
      componentProps: {
        placeholder: '请输入手机号',
        allowClear: true,
      },
    },
    {
      component: 'RadioGroup',
      fieldName: 'sex',
      label: '性别',
      rules: 'required',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: '保密', value: 0 },
          { label: '男', value: 1 },
          { label: '女', value: 2 },
        ],
      },
      defaultValue: 0,
    },
  ];
}

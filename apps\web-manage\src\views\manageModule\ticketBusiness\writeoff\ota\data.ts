import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import { useAccessStore } from '@vben/stores';
import { getAllScenicList, getTicketAllList } from '#/api/manageModule';
import { toRefs } from 'vue';
const { accessAllEnums } = toRefs(useAccessStore());

export function useVerifyLogGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'ApiSelect',
      fieldName: 'scenicId',
      label: '所属景区',
      hideLabel: true,
      componentProps: {
        // 菜单接口转options格式
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.scenicName,
            value: item.id,
          }));
        },
        // 菜单接口
        api: getAllScenicList,
        placeholder: '请选择所属景区',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'receiptCode',
      label: '验券码',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入验券码',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '状态',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择状态',
        allowClear: true,
        options: [
          { label: '已核销', value: 1 },
          { label: '已撤销', value: 2 },
        ],
      },
    },
    {
      component: 'RangePicker',
      fieldName: 'verificationDate',
      label: '开卡时间',
      hideLabel: true,
      componentProps: {
        format: 'YYYY-MM-DD',
        allowClear: true,
        placeholder: ['核销开始日期', '核销结束日期'],
        separator: '至',
        valueFormat: 'YYYY-MM-DD',
      },
    },
  ];
}

export function useVerifyLogColumns<T = any>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  return [
    {
      title: '验券码',
      field: 'receiptCode',
      width: 120,
      align: 'center',
      fixed: 'left',
    },
    {
      title: '核销数量',
      field: 'count',
      width: 120,
      align: 'center',
    },
    {
      title: '核销时间',
      field: 'createdAt',
      width: 150,
      align: 'center',
    },
    {
      title: '用户手机号',
      field: 'mobile',
      width: 120,
      align: 'center',
    },
    {
      title: '状态',
      field: 'status',
      width: 120,
      align: 'center',
      cellRender: {
        name: 'CellTag',
        options: [
          { value: 1, label: '已核销', color: 'success' },
          { value: 2, label: '已撤销', color: 'warning' },
        ],
      },
    },
    {
      title: '美团订单号',
      field: 'verifyOrderId',
      width: 150,
      align: 'center',
    },
    {
      title: '门票订单号',
      field: 'orderNo',
      width: 180,
      align: 'center',
    },
    {
      title: '核销景区',
      field: 'scenicInfo',
      width: 120,
      align: 'center',
      formatter: ({ row }: any) => {
        return row.scenicInfo?.scenicName;
      },
    },
    {
      title: '核销人',
      field: 'verifyUser',
      width: 120,
      align: 'center',
      formatter: ({ row }: any) => {
        return row.adminUserInfo?.name;
      },
    },
    {
      title: '操作',
      field: 'operation',
      cellRender: {
        attrs: {
          nameField: 'title',
          nameTitle: '验券码',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            text: '撤销',
            code: 'cancel',
            danger: true,
            show: (row: any) => {
              return row.status === 1;
            },
          },
          {
            text:'出票',
            code: 'outTicket',
            show: (row: any) => {
              return row.status === 1;
            },
          }
        ],
      },
      fixed: 'right',
      width: 120,
      align: 'center',
    },
  ];
}

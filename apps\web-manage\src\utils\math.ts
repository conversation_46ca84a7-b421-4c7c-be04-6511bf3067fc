import { BigNumber } from 'bignumber.js';

export default {
  // 加
  add(a: any, b: any) {
    a = BigNumber(a);
    b = BigNumber(b);
    return a.plus(b).toNumber(); // 结果需要用toNumber转为普通数字
  },
  // 减
  minus(a: any, b: any) {
    a = BigNumber(a);
    b = BigNumber(b);
    return a.minus(b).toNumber();
  },
  // 乘
  multiply(a: any, b: any) {
    a = BigNumber(a);
    b = BigNumber(b);
    return a.multipliedBy(b).toNumber();
  },
  // 除
  divide(a: any, b: any) {
    a = BigNumber(a);
    b = BigNumber(b);
    return a.dividedBy(b).toNumber();
  },
};

// format()方法是格式化校验方法，把math方法计算出的值以字符串的形式显示最终的结果

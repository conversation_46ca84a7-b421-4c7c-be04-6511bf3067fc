<script lang="ts" setup>
import { ref } from 'vue';

import { useAuthStore } from '#/store';
const authStore = useAuthStore();

import { useVbenModal } from '@vben/common-ui';

import {
  Form,
  FormItem,
  Input,
  Radio,
  RadioGroup,
  message,
} from 'ant-design-vue';

import { getMyInfo, updateAdminUser } from '#/api/windowModule/index';

import customUpTem from '#/components/customUp/index.vue';

// ========================================================================================
const open = async () => {
  await getInfo();
  modalApi.open();
};
defineExpose({ open });

// =========================================================================================
const formRef: any = ref(null);
const rules: any = {
  avatar: [
    { type: 'array', required: true, message: '请上传头像', trigger: 'change' },
  ],
  name: [{ required: true, message: '请输入姓名' }],
  phone: [{ required: true, message: '请输入手机号' }],
};
const formState: any = ref({
  avatar: [],
  name: '',
  phone: '',
  sex: 0,
});

const getInfo = async () => {
  const resData = await getMyInfo({});
  for (const key in formState.value) {
    formState.value[key] = resData[key];
  }
  if (resData.avatar) {
    formState.value.avatar = [resData.avatar];
  }
};

const [Modal, modalApi] = useVbenModal({
  onConfirm() {
    formRef.value.validate().then(async () => {
      let params = { ...formState.value };
      params.avatar = params.avatar[0] || '';
      await updateAdminUser(params);
      message.success('修改成功');
      authStore.getUserInfo(false);
      modalApi.close();
    });
  },
});
</script>
<template>
  <Modal title="修改信息" destroy-on-close>
    <Form
      :model="formState"
      class="px-5 pt-10"
      :label-col="{ span: 4 }"
      :rules="rules"
      ref="formRef"
    >
      <FormItem label="头像" name="avatar">
        <customUpTem
          :isUp="true"
          :maxCount="1"
          accept="image"
          fileTypeTag="avatar"
          v-model:value="formState.avatar"
        />
      </FormItem>

      <FormItem label="姓名" name="name">
        <Input v-model:value="formState.name" placeholder="请输入" />
      </FormItem>

      <FormItem label="手机号" name="phone">
        <Input v-model:value="formState.phone" placeholder="请输入" />
      </FormItem>

      <FormItem label="性别" name="sex">
        <RadioGroup v-model:value="formState.sex">
          <Radio :value="0">保密</Radio>
          <Radio :value="1">男</Radio>
          <Radio :value="2">女</Radio>
        </RadioGroup>
      </FormItem>
    </Form>
  </Modal>
</template>

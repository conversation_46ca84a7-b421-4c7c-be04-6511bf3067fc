import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

async function receiptPrepare(params: Recordable<any>) {
  return requestClient.get('/tkt/verification/meituan/tuangou/receiptPrepare', {
    params,
  });
}
async function getMeituanTuangouVerifyLog(params: Recordable<any>) {
  return requestClient.get('/tkt/verification/meituan/tuangou/verifyLog', {
    params,
  });
}
async function consumeMeituanTuangou(data: Recordable<any>) {
  return requestClient.post('/tkt/verification/meituan/tuangou/consume', data);
}
async function consumeReverseMeituanTuangou(data: Recordable<any>) {
  return requestClient.post(
    '/tkt/verification/meituan/tuangou/consumeReverse',
    data,
  );
}

export {
  receiptPrepare,
  getMeituanTuangouVerifyLog,
  consumeMeituanTuangou,
  consumeReverseMeituanTuangou,
};

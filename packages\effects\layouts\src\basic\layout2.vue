<script lang="ts" setup>
import { useAppConfig } from '@vben/hooks';
import { usePreferences } from '@vben/preferences';
const { isDark } = usePreferences();

const { cdnURL } = useAppConfig(import.meta.env, import.meta.env.PROD);

import { ThemeToggle } from '../widgets';
import { LayoutContent } from './content';
</script>

<template>
  <div
    class="warpMain flex h-screen w-screen flex-col"
    :style="`background: url(${cdnURL}/window/${!isDark ? 'bgW' : 'bgB'}.svg); background-size: cover; background-position: center;`"
  >
    <!-- 头部区域 -->
    <div
      class="warpHeader"
      :style="`background: url(${cdnURL}/window/${!isDark ? 'headW' : 'headB'}.svg); background-size: cover; background-position: top center;`"
    >
      <div class="warpBox m-auto flex items-center justify-between">
        <div class="flex-1">
          <slot name="header"></slot>
        </div>
        <div class="pt-0.5">
          <ThemeToggle />
        </div>
      </div>
    </div>

    <!-- 主体内容 -->
    <div class="warpCenter flex flex-1 flex-col overflow-hidden pt-4">
      <div class="warpBox m-auto max-h-full w-full flex-1">
        <LayoutContent />
      </div>
    </div>

    <!-- 底部 -->
    <div v-if="$slots.footer" class="warpFooter">
      <div class="warpBox m-auto">
        <slot name="footer"></slot>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.warpMain {
  // background: linear-gradient(180deg, #3872fd, #06bdbb, #299a0c);
  // background-color: hsl(var(--background-deep));
  .warpBox {
    max-width: 1200px;
  }
  .warpHeader {
    // background-color: hsl(var(--header));
    // box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 8px 5px;
  }
  .warpCenter {
    // background-color: hsl(var(--background-deep));
  }
  .warpFooter {
    // background-color: hsl(var(--background-deep));
  }
}
</style>

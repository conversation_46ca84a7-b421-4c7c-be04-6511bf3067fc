<script lang="ts" setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';

import {
  PayCircleOutlined,
  SendOutlined,
  SwapOutlined,
} from '@ant-design/icons-vue';

// ======================================================================================================================
const router = useRouter();
const goBtn = (path: string) => {
  router.push(path);
};

// ======================================================================================================================
const listArr = ref([
  { title: '人工售卡', path: '/window/ticketSales' },
  { title: '售票统计', path: '/' },
  { title: '售取票统计', path: '/' },
  { title: '扫码退款', path: '/' },
  { title: '重新打印', path: '/' },
  { title: '异常订单同步', path: '/' },
]);
</script>

<template>
  <div class="flex-center h-full">
    <div class="w-full">
      <div class="grid grid-cols-4 gap-10">
        <div></div>
        <div
          class="flex-center bg-primary cursor-pointer rounded-lg px-3 py-10 text-white"
          @click="goBtn('/window/ticketSales')"
        >
          <PayCircleOutlined class="text-6xl" />
          <div class="ml-5 text-center text-2xl">售票</div>
        </div>
        <div
          class="flex-center bg-primary cursor-pointer rounded-lg px-3 py-10 text-white"
          @click="goBtn('/window/ticketCollect')"
        >
          <SendOutlined class="text-6xl" />
          <div class="ml-5 text-center text-2xl">取票</div>
        </div>
        <div></div>
      </div>

      <div class="mt-20 grid grid-cols-4 gap-10">
        <div
          v-for="(item, index) in listArr"
          :key="index"
          class="flex-center bg-primary cursor-pointer rounded-lg px-3 py-8 text-white"
        >
          <SwapOutlined class="text-5xl" />
          <div class="ml-5 text-center text-2xl">{{ item.title }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>

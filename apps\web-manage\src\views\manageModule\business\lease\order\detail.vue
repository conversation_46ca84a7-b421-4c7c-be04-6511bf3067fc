<script setup lang="ts">
import { ref, onMounted, toRefs } from 'vue';
import { Page } from '@vben/common-ui';
import {
  PageHeader,
  Card,
  Descriptions,
  DescriptionsItem,
  Table,
} from 'ant-design-vue';
import type { TableColumnType } from 'ant-design-vue';
import { useRoute } from 'vue-router';
import { getRentalOrderInfo, getRentalOrderLog } from '#/api/manageModule';
import { useAccessStore } from '@vben/stores';
const { accessAllEnums } = toRefs(useAccessStore());
import { useLogTableSchema } from './data';

const route = useRoute();
const orderId = ref<any>(route.query.id);
const orderInfo = ref<any>({});
const getOrderInfoData = async () => {
  const res = await getRentalOrderInfo(orderId.value);
  orderInfo.value = res;
};

const params = ref({
  orderId: orderId.value,
  page: 1,
  pageSize: 10,
});
const logList = ref<any>([]);
const total = ref(0);
const getRentalOrderLogData = async () => {
  const res = await getRentalOrderLog(params.value);
  logList.value = res.list;
  total.value = res.total;
};

onMounted(() => {
  getOrderInfoData();
  getRentalOrderLogData();
});

const filterText = (arr: any[], val: any) => {
  return arr.find((item: any) => item.value === val)?.label;
};
const columns: TableColumnType[] = [
  {
    title: '凭证码',
    dataIndex: 'verificationCode',
    width: 180,
    customRender: ({ record }: any) => {
      return record.verificationCode || '--';
    },
  },
  {
    title: '物品名称',
    dataIndex: 'goodsName',
    width: 150,
    align: 'center',
    customRender: ({ record }: any) => {
      return record.goodsName || '--';
    },
  },
  {
    title: '计费方式',
    dataIndex: 'billingMethod',
    width: 120,
    align: 'center',
    customRender: ({ record }: any) => {
      return record.billingMethod === 1
        ? '按次计费'
        : record.billingMethod === 2
          ? '按分钟计费'
          : '按小时计费';
    },
  },
  {
    title: '租赁数量',
    dataIndex: 'rentalNum',
    width: 100,
    align: 'center',
    customRender: ({ record }: any) => {
      return record.rentalNum || '--';
    },
  },
  {
    title: '租金',
    dataIndex: 'rentalPrice',
    width: 100,
    align: 'center',
    customRender: ({ record }: any) => {
      return record.rentalPrice || '--';
    },
  },
  {
    title: '押金',
    dataIndex: 'depositPrice',
    width: 100,
    align: 'center',
    customRender: ({ record }: any) => {
      return record.depositPrice || '--';
    },
  },
  {
    title: '损坏补费',
    dataIndex: 'damagePrice',
    width: 100,
    align: 'center',
    customRender: ({ record }: any) => {
      return record.damagePrice || '--';
    },
  },
  {
    title: '物品状态',
    dataIndex: 'orderStatus',
    width: 120,
    align: 'center',
    customRender: ({ record }: any) => {
      return accessAllEnums.value?.rentalOrderStatus.list.find(
        (item: any) => item.value === record.orderStatus,
      )?.label;
    },
  },
  {
    title: '取货时间',
    dataIndex: 'takeTime',
    width: 180,
    align: 'center',
    customRender: ({ record }: any) => {
      return record.takeTime || '--';
    },
  },
  {
    title: '归还时间',
    dataIndex: 'backTime',
    width: 180,
    align: 'center',
    customRender: ({ record }: any) => {
      return record.backTime || '--';
    },
  },
];
</script>
<template>
  <Page auto-content-height>
    <div class="bg-card">
      <PageHeader
        title="租赁订单详情"
        class="p-3"
        @back="() => $router.back()"
      ></PageHeader>
      <div class="px-3 pb-3">
        <Card title="订单信息" class="mb-4">
          <Descriptions>
            <DescriptionsItem label="订单号">{{
              orderInfo.orderNo
            }}</DescriptionsItem>
            <DescriptionsItem label="下单时间">{{
              orderInfo.orderTime
            }}</DescriptionsItem>
            <DescriptionsItem label="所属景区">{{
              orderInfo.scenicInfo?.scenicName
            }}</DescriptionsItem>
            <DescriptionsItem label="订单状态">{{
              filterText(
                accessAllEnums?.rentalOrderStatus.list,
                orderInfo.orderStatus,
              )
            }}</DescriptionsItem>
            <DescriptionsItem label="支付状态">{{
              filterText(
                accessAllEnums?.orderPayStatus.list,
                orderInfo.payStatus,
              )
            }}</DescriptionsItem>
            <DescriptionsItem label="支付方式">{{
              filterText(
                accessAllEnums?.orderPayMethod.list,
                orderInfo.payMethod,
              )
            }}</DescriptionsItem>
            <DescriptionsItem label="订单来源">{{
              filterText(
                accessAllEnums?.orderSource.list,
                orderInfo.orderSource,
              )
            }}</DescriptionsItem>
            <DescriptionsItem label="联系人">
              {{ orderInfo.userInfo?.name }}
              <span class="ml-2" v-if="orderInfo.userInfo?.phone">{{
                orderInfo.userInfo?.phone
              }}</span>
            </DescriptionsItem>
            <DescriptionsItem label="租金总额">
              {{ orderInfo.rentalPrice }}
            </DescriptionsItem>
            <DescriptionsItem label="押金总额">
              {{ orderInfo.depositPrice }}
            </DescriptionsItem>
            <DescriptionsItem label="损坏补费总额">
              {{ orderInfo.damagePrice }}
            </DescriptionsItem>
          </Descriptions>
        </Card>
        <Card title="物品信息" class="mb-5">
          <Table
            :columns="columns"
            :data-source="orderInfo.orderItemList"
            :pagination="false"
          ></Table>
        </Card>
        <Card title="订单日志">
          <Table
            :columns="useLogTableSchema()"
            :dataSource="logList"
            :pagination="{
              current: params.page,
              pageSize: params.pageSize,
              total: total,
              onChange: (page, pageSize) => {
                params.page = page;
                params.pageSize = pageSize;
                getRentalOrderLogData();
              },
              showTotal: (total) => `共 ${total} 条`,
            }"
          >
            <template #bodyCell="{ column, text, record }">
              <template v-if="column.dataIndex == 'touristName'">
                <p v-if="record.touristName">
                  {{ record.touristName }}<br />{{ record.touristIdcard }}
                </p>
                <p v-else>--</p>
              </template>
              <template v-if="column.dataIndex == 'ticketName'">
                <p v-if="record.adminUserInfo">
                  {{ record.adminUserInfo?.name }}<br />{{
                    record.adminUserInfo?.phone
                  }}
                </p>
                <p v-else>--</p>
              </template>
            </template>
          </Table>
        </Card>
      </div>
    </div>
  </Page>
</template>

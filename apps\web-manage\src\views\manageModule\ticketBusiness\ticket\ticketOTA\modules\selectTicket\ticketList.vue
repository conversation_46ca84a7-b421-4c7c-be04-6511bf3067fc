<script setup lang="ts">
import type { Recordable } from '@vben/types';
import { computed, ref, toRefs, watch, defineProps, defineEmits } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { Input, Select, Button, Table, Form, FormItem } from 'ant-design-vue';
import { getTicketList } from '#/api/manageModule';
import { useAccessStore } from '@vben/stores';
const { accessAllEnums } = toRefs(useAccessStore());

const emits = defineEmits(['change']);
const props = defineProps({
  selectedTickets: {
    type: Array,
    default: () => [],
  },
  scenicIds: {
    type: [String, Number],
    default: undefined,
  },
});

const [Model, modelApi] = useVbenModal({
  title: '门票列表',
  async onConfirm() {
    await handleSubmit();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      setTimeout(() => {
        getTicketLists();
      }, 0);
    }
  },
});
const columns = ref([
  {
    title: '门票名称',
    dataIndex: 'ticketName',
  },
  {
    title: '门票类型',
    dataIndex: 'model',
  },
  {
    title: '销售价',
    dataIndex: 'sellingPrice',
  },
]);
const params = ref({
  name: undefined,
  page: 1,
  pageSize: 10,
  model: undefined,
  scenicId: props.scenicIds
});
watch(
  () => props.scenicIds,
  (val) => {
    params.value.scenicId = val;
  },
  { immediate: true, deep: true },
);

const ticketList = ref<any[]>([]);
const total = ref(0);
const getTicketLists = async () => {
  const res = await getTicketList(params.value);
  ticketList.value = res.list;
  total.value = res.total;
};
const resetForm = () => {
  params.value = {
    name: undefined,
    page: 1,
    pageSize: 10,
    model: undefined,
    scenicId: props.scenicIds,
  };
  getTicketLists();
};
const handleSearch = () => {
  params.value.page = 1;
  getTicketLists();
};


const selectedRowKeys = ref<any>(null);
const selectedRows = ref<any>(null);

watch(
  () => props.selectedTickets,
  (val) => {
    if (val && val.length > 0) {
      console.log(val, 'selectedTickets');
      // 单选模式，只取第一个
      selectedRows.value = val[0];
      selectedRowKeys.value = val[0]?.id;
    } else {
      selectedRows.value = null;
      selectedRowKeys.value = null;
    }
  },
  { immediate: true, deep: true },
);

const rowSelection = computed(() => ({
  type: 'radio' as const, // 设置为单选模式
  selectedRowKeys: selectedRowKeys.value ? [selectedRowKeys.value] : [],
  onChange: (
    currentSelectedKeys: (string | number)[],
    currentSelectedRows: any[],
  ) => {
    // 单选模式，只保留最新选择的一个
    if (currentSelectedKeys.length > 0 && currentSelectedRows.length > 0) {
      selectedRowKeys.value = currentSelectedKeys[0];
      selectedRows.value = currentSelectedRows[0];
    } else {
      selectedRowKeys.value = null;
      selectedRows.value = null;
    }
  },
  onSelect: (record: any, selected: boolean) => {
    if (selected) {
      selectedRows.value = record;
      selectedRowKeys.value = record.id;
    } else {
      selectedRows.value = null;
      selectedRowKeys.value = null;
    }
  },
}));

const handleSubmit = () => {
  // 单选模式，将选中的门票包装成数组返回
  const result = selectedRows.value ? [selectedRows.value] : [];
  emits(
    'change',
    result
  );
  modelApi.close();
};

const filterModel = (val: any) => {
  return accessAllEnums.value.ticketModel.list.find(
    (item: any) => item.value === val,
  )?.label;
}
</script>
<template>
  <Model class="w-[50%]" title="门票列表">
    <Form layout="inline" :model="params" class="mb-2">
      <FormItem label="门票名称">
        <Input v-model:value="params.name" allowClear placeholder="请输入门票名称" />
      </FormItem>
      <FormItem label="门票类型">
        <Select v-model:value="params.model" :options="accessAllEnums.ticketModel.list" class="!w-[200px]" allowClear
          placeholder="请选择门票类型" />
      </FormItem>
      <FormItem>
        <Button @click="resetForm()">重置</Button>
        <Button type="primary" class="ml-2" @click="handleSearch()">搜索</Button>
      </FormItem>
    </Form>
    <div>
      <Table :rowSelection="rowSelection" :columns="columns" :dataSource="ticketList" rowKey="id" :pagination="{
        current: params.page,
        pageSize: params.pageSize,
        total: total,
        onChange: (page, pageSize) => {
          params.page = page;
          params.pageSize = pageSize;
          getTicketLists();
        },
        showTotal: (total) => `共 ${total} 条`,
      }" bordered>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'model'">
            {{ filterModel(record.model) }}
          </template>
        </template>
      </Table>
    </div>
  </Model>
</template>

class LocalStorage {
  // 清除localStorage中的所有数据
  clear() {
    try {
      localStorage.clear();
    } catch (error) {
      console.error('LocalStorage clear error:', error);
    }
  }

  // 从localStorage获取数据
  get(key: string) {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : null;
    } catch (error) {
      console.error('LocalStorage getItem error:', error);
      return null;
    }
  }

  // 移除localStorage中的数据
  remove(key: string) {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error('LocalStorage removeItem error:', error);
    }
  }

  // 设置数据到localStorage
  set(key: string, value: any) {
    // if (typeof value === 'string') {
    //   localStorage.setItem(key, value);
    // } else {
    localStorage.setItem(key, JSON.stringify(value));
    // }
  }
}

export default new LocalStorage();

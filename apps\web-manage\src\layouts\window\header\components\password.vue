<script setup lang="ts">
import { useVbenModal } from '@vben/common-ui';
import { z } from '#/adapter/form';
import { useVbenForm } from '#/adapter/form';
import { updateAdminPassword } from '#/api/manageModule';
import { message } from 'ant-design-vue';

// ======================================================================================================================
const [Modal, modalApi] = useVbenModal({
  title: '修改密码',
  draggable: true,
  destroyOnClose: true,

  async onConfirm() {
    passwordFormApi.validate();
    const values = await passwordFormApi.getValues();
    if (!values) return;
    await updateAdminPassword(values);
    message.success('修改成功');
    modalApi.close();
  },
});
const open = () => {
  modalApi.open();
};
defineExpose({ open });

// =====================================================================================================================

const [PasswordForm, passwordFormApi] = useVbenForm({
  showDefaultActions: false,
  schema: [
    {
      component: 'VbenInputPassword',
      fieldName: 'password',
      label: '旧密码',
      rules: 'required',
      componentProps: {
        placeholder: '请输入旧密码',
        allowClear: true,
      },
    },
    {
      component: 'VbenInputPassword',
      componentProps: {
        passwordStrength: true,
        placeholder: '请输入新密码',
      },
      fieldName: 'newPassword',
      label: '新密码',
      renderComponentContent() {
        return {
          strengthText: () => '至少6位字符，支持数字，字母，符号',
        };
      },
      rules: z.string().min(6, { message: '新密码至少6位字符' }),
    },
    {
      component: 'VbenInputPassword',
      fieldName: 'rePassword',
      label: '确认密码',
      rules: 'required',
      componentProps: {
        placeholder: '请确认新密码',
        allowClear: true,
        visible: true,
      },
      dependencies: {
        rules(values) {
          const { newPassword } = values;
          return z
            .string({ required_error: '请确认新密码' })
            .min(1, { message: '请确认新密码' })
            .refine((value) => value === newPassword, {
              message: '两次输入的密码不一致',
            });
        },
        triggerFields: ['newPassword'],
      },
    },
  ],
  commonConfig: {
    labelClass: 'w-[70px]',
  },
});
</script>

<template>
  <Modal>
    <div class="pr-3 pt-5">
      <PasswordForm ref="form" />
    </div>
  </Modal>
</template>

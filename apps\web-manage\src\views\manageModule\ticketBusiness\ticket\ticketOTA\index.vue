<script setup lang="ts">
import { ref } from 'vue';
import { Page } from '@vben/common-ui';
import { RadioGroup, Radio } from 'ant-design-vue';
import MtList from './modules/mtTg/mtList.vue';
const currentType = ref(1);
</script>
<template>
  <!-- <Page auto-content-height>
    <div class="bg-card p-2">
      <RadioGroup
        button-style="solid"
        optionType="button"
        v-model:value="currentType"
      >
        <Radio :value="1">美团</Radio>
        <Radio :value="2">抖音</Radio>
      </RadioGroup>
    </div>
    <MtList style="height: calc(100% - 48px);"></MtList>
  </Page> -->
  <div class="flex h-full flex-col p-4">
    <div class="bg-card p-2">
      <RadioGroup
        button-style="solid"
        optionType="button"
        v-model:value="currentType"
      >
        <Radio :value="1">美团团购</Radio>
        <Radio :value="2">抖音团购</Radio>
      </RadioGroup>
    </div>
    <MtList v-if="currentType == 1"></MtList>
  </div>
</template>

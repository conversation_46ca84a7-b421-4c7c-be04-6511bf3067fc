<script lang="ts" setup>
import { ref, watch } from 'vue';
import { InputGroup, Input, Button, Space } from 'ant-design-vue';

let props = defineProps(['value', 'row']);

let ticketNum: any = ref(0);
let sendParams: any = ref([]);
watch(
  () => props.row,
  (newValue) => {
    if (!newValue.ticketNum) return;

    if (!sendParams.value.length && newValue.authenticationType === 2) {
      sendParams.value.push({ name: '', idcard: '' });
    }

    if (newValue.authenticationType === 3) {
      if (ticketNum.value < newValue.ticketNum) {
        let num = newValue.ticketNum - ticketNum.value;
        for (let i = 0; i < num; i++) {
          sendParams.value.push({ name: '', idcard: '' });
        }
      } else if (ticketNum.value > newValue.ticketNum) {
        let num = ticketNum.value - newValue.ticketNum;
        for (let i = 0; i < num; i++) {
          sendParams.value.pop();
        }
      }
      ticketNum.value = newValue.ticketNum;
    }
  },
  { immediate: true, deep: true },
);

let emits = defineEmits(['update:value']);
watch(
  () => sendParams.value,
  (newValue) => {
    emits('update:value', newValue);
  },
  { immediate: true, deep: true },
);
</script>

<template>
  <Space direction="vertical" class="w-full">
    <div class="flex gap-3" v-for="(item, index) in sendParams" :key="index">
      <div class="flex w-1/2 items-center">
        <div class="mr-2">游客:</div>
        <Input
          class="flex-1"
          v-model:value="item.name"
          placeholder="请输入游客姓名"
          allowClear
        />
      </div>
      <div class="flex w-1/2 items-center">
        <div class="mr-2">证件号:</div>
        <InputGroup compact class="!flex flex-1">
          <Input
            v-model:value="item.idcard"
            placeholder="请输入证件号"
            allowClear
          />
          <Button>读取</Button>
        </InputGroup>
      </div>
    </div>
  </Space>
</template>

<style lang="scss" scoped></style>
